<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矿业AI管理系统 - 首页</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        /* 首页特定样式 */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: var(--spacing-lg) var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
        }

        .dashboard-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .dashboard-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .action-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .action-card-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .action-card-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .action-card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .action-card-description {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: var(--spacing-sm);
        }

        .action-card-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-sm);
            border-top: 1px solid var(--border-color);
        }

        .action-card-stat {
            text-align: center;
        }

        .action-card-stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .action-card-stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .system-status {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-item-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .recent-alerts {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid var(--border-color);
        }

        .alert-item:last-child {
            border-bottom: none;
        }

        .alert-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 14px;
        }

        .alert-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 图标颜色 */
        .icon-dust { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .icon-gas { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .icon-water { background: linear-gradient(135deg, #06b6d4, #0891b2); }
        .icon-equipment { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .icon-personnel { background: linear-gradient(135deg, #10b981, #059669); }
        .icon-data { background: linear-gradient(135deg, #3b82f6, #2563eb); }
        .icon-operations { background: linear-gradient(135deg, #64748b, #475569); }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('index.html', '系统首页', ['首页']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 仪表板头部 -->
            <div class="dashboard-header">
                <div class="dashboard-title">
                    <i class="fas fa-tachometer-alt"></i>
                    矿业AI智能监控平台
                </div>
                <div class="dashboard-subtitle">
                    实时监控 · 智能预警 · 安全生产 · 数据驱动
                </div>
            </div>

            <!-- 关键指标统计 -->
            <div class="stats-grid">
                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">安全运行天数</div>
                                <div class="data-card-value" id="safetyDays">365 <span class="data-card-unit">天</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">今日报警次数</div>
                                <div class="data-card-value" id="todayAlerts">3 <span class="data-card-unit">次</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">在线人员</div>
                                <div class="data-card-value" id="onlinePersonnel">127 <span class="data-card-unit">人</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">设备运行率</div>
                                <div class="data-card-value" id="equipmentRate">98.5 <span class="data-card-unit">%</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作模块 -->
            <div class="quick-actions">
                <div class="action-card" onclick="location.href='dust-control.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-dust">
                            <i class="fas fa-wind"></i>
                        </div>
                        <div class="action-card-title">粉尘监控</div>
                    </div>
                    <div class="action-card-description">
                        高清视频采集与图像处理，智能粉尘识别与抑尘控制系统
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value" id="dustLevel">12.5</div>
                            <div class="action-card-stat-label">mg/m³</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">8</div>
                            <div class="action-card-stat-label">监测点</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">正常</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='gas-monitoring.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-gas">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="action-card-title">瓦斯监测</div>
                    </div>
                    <div class="action-card-description">
                        多点瓦斯、甲烷、一氧化碳监测，AI建模浓度分布与预警
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value" id="gasLevel">0.3</div>
                            <div class="action-card-stat-label">%</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">15</div>
                            <div class="action-card-stat-label">监测点</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">安全</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='drainage-control.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-water">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="action-card-title">排水控制</div>
                    </div>
                    <div class="action-card-description">
                        智能水位监测，LSTM预测模型，自动水泵控制与故障诊断
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value" id="waterLevel">2.3</div>
                            <div class="action-card-stat-label">米</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">6</div>
                            <div class="action-card-stat-label">水泵</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">运行</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='equipment-monitoring.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-equipment">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="action-card-title">设备监控</div>
                    </div>
                    <div class="action-card-description">
                        设备状态监控，预测性维护，异常检测与故障预警
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">42</div>
                            <div class="action-card-stat-label">设备</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">98.5</div>
                            <div class="action-card-stat-label">%运行率</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">良好</div>
                            <div class="action-card-stat-label">健康度</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='personnel-safety.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-personnel">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <div class="action-card-title">人员安全</div>
                    </div>
                    <div class="action-card-description">
                        人员定位跟踪，行为分析，安全防护监测与预警
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value" id="personnelCount">127</div>
                            <div class="action-card-stat-label">在线</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">12</div>
                            <div class="action-card-stat-label">区域</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">安全</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='data-platform.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-data">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="action-card-title">数据中台</div>
                    </div>
                    <div class="action-card-description">
                        数据统一采集治理，AI模型训练推理，智能分析平台
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">1.2TB</div>
                            <div class="action-card-stat-label">数据量</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">15</div>
                            <div class="action-card-stat-label">模型</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">运行</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='space-management.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-space">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="action-card-title">空间管理</div>
                    </div>
                    <div class="action-card-description">
                        矿区空间布置管理，区域权限控制，人员定位联动
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">12</div>
                            <div class="action-card-stat-label">区域</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">127</div>
                            <div class="action-card-stat-label">在线人员</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">正常</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='inspection-management.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-inspection">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="action-card-title">巡检管理</div>
                    </div>
                    <div class="action-card-description">
                        智能巡检任务管理，移动端支持，闭环问题跟踪
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">24</div>
                            <div class="action-card-stat-label">今日任务</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">87.5%</div>
                            <div class="action-card-stat-label">完成率</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">进行中</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>

                <div class="action-card" onclick="location.href='drone-patrol.html'">
                    <div class="action-card-header">
                        <div class="action-card-icon icon-drone">
                            <i class="fas fa-helicopter"></i>
                        </div>
                        <div class="action-card-title">无人机巡航</div>
                    </div>
                    <div class="action-card-description">
                        智能无人机巡航，AI图像识别，风险自动预警
                    </div>
                    <div class="action-card-stats">
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">3</div>
                            <div class="action-card-stat-label">在线</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">5</div>
                            <div class="action-card-stat-label">风险发现</div>
                        </div>
                        <div class="action-card-stat">
                            <div class="action-card-stat-value">巡航中</div>
                            <div class="action-card-stat-label">状态</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部信息区域 -->
            <div class="grid grid-cols-2">
                <!-- 系统状态 -->
                <div class="system-status">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-server"></i>
                        系统状态
                    </h3>
                    <div class="status-item">
                        <span class="status-item-label">数据采集服务</span>
                        <span class="status-indicator status-normal">
                            <i class="fas fa-check-circle"></i>
                            正常
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-item-label">AI推理服务</span>
                        <span class="status-indicator status-normal">
                            <i class="fas fa-check-circle"></i>
                            正常
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-item-label">报警服务</span>
                        <span class="status-indicator status-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            警告
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-item-label">数据库服务</span>
                        <span class="status-indicator status-normal">
                            <i class="fas fa-check-circle"></i>
                            正常
                        </span>
                    </div>
                </div>

                <!-- 最近报警 -->
                <div class="recent-alerts">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-bell"></i>
                        最近报警
                    </h3>
                    <div class="alert-item">
                        <div class="alert-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">3号工作面粉尘浓度偏高</div>
                            <div class="alert-time">2分钟前</div>
                        </div>
                    </div>
                    <div class="alert-item">
                        <div class="alert-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">排水系统自动启动成功</div>
                            <div class="alert-time">15分钟前</div>
                        </div>
                    </div>
                    <div class="alert-item">
                        <div class="alert-icon" style="background: rgba(239, 68, 68, 0.1); color: var(--danger-color);">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">2号皮带机振动异常</div>
                            <div class="alert-time">1小时前</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('dustLevel', 8, 20, 'mg/m³', 1);
        MiningAI.simulateRealTimeData('gasLevel', 0.1, 0.5, '%', 2);
        MiningAI.simulateRealTimeData('waterLevel', 1.5, 3.0, '米', 1);
        MiningAI.simulateRealTimeData('personnelCount', 120, 135, '人', 0);

        // 显示欢迎消息
        setTimeout(() => {
            MiningAI.showNotification('欢迎使用矿业AI管理系统！', 'success');
        }, 1000);
    </script>
</body>
</html>
