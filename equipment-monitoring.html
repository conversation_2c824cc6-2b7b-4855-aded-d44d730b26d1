<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .equipment-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .equipment-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .equipment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .equipment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .equipment-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .equipment-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .equipment-type {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .equipment-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xs);
            margin: var(--spacing-sm) 0;
        }

        .metric-item {
            text-align: center;
            padding: var(--spacing-xs);
            background: var(--bg-color);
            border-radius: var(--radius-sm);
        }

        .metric-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .metric-label {
            font-size: 10px;
            color: var(--text-secondary);
        }

        .health-score {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }

        .health-bar {
            flex: 1;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
        }

        .health-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .health-excellent { background: var(--success-color); }
        .health-good { background: #10b981; }
        .health-fair { background: var(--warning-color); }
        .health-poor { background: var(--danger-color); }

        .equipment-detail {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .detail-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .sensor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .sensor-card {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            text-align: center;
        }

        .sensor-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .sensor-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .trend-chart {
            height: 200px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .alert-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .alert-item:last-child {
            margin-bottom: 0;
        }

        .alert-critical {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning-color);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            border-left: 4px solid var(--info-color);
        }

        .maintenance-schedule {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .schedule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .schedule-item:last-child {
            border-bottom: none;
        }

        .schedule-info {
            flex: 1;
        }

        .schedule-equipment {
            font-weight: 500;
            color: var(--text-primary);
        }

        .schedule-task {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .schedule-time {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: right;
        }

        .predictive-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .analysis-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .analysis-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .analysis-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .analysis-content {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        .equipment-status-running {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .equipment-status-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .equipment-status-fault {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .equipment-status-maintenance {
            background: linear-gradient(135deg, #64748b, #475569);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('equipment-monitoring.html', '设备监控', ['首页', '设备监控']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 设备概览 -->
            <div class="equipment-overview">
                <div class="equipment-card" onclick="selectEquipment('fan1')">
                    <div class="equipment-header">
                        <div class="equipment-icon equipment-status-running">
                            <i class="fas fa-fan"></i>
                        </div>
                        <span class="status-indicator status-normal">
                            <i class="fas fa-check-circle"></i>
                            运行
                        </span>
                    </div>
                    <div class="equipment-name">主通风机1号</div>
                    <div class="equipment-type">离心式通风机</div>
                    <div class="equipment-metrics">
                        <div class="metric-item">
                            <div class="metric-value" id="fan1-speed">1450</div>
                            <div class="metric-label">转速(rpm)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="fan1-power">85</div>
                            <div class="metric-label">功率(kW)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="fan1-temp">45</div>
                            <div class="metric-label">温度(°C)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="fan1-vibration">2.1</div>
                            <div class="metric-label">振动(mm/s)</div>
                        </div>
                    </div>
                    <div class="health-score">
                        <span style="font-size: 12px;">健康度:</span>
                        <div class="health-bar">
                            <div class="health-fill health-excellent" style="width: 92%;"></div>
                        </div>
                        <span style="font-size: 12px; font-weight: 600;">92%</span>
                    </div>
                </div>

                <div class="equipment-card" onclick="selectEquipment('belt1')">
                    <div class="equipment-header">
                        <div class="equipment-icon equipment-status-warning">
                            <i class="fas fa-conveyor-belt"></i>
                        </div>
                        <span class="status-indicator status-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            警告
                        </span>
                    </div>
                    <div class="equipment-name">皮带机2号</div>
                    <div class="equipment-type">带式输送机</div>
                    <div class="equipment-metrics">
                        <div class="metric-item">
                            <div class="metric-value" id="belt1-speed">1.2</div>
                            <div class="metric-label">速度(m/s)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="belt1-load">75</div>
                            <div class="metric-label">负载(%)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="belt1-temp">68</div>
                            <div class="metric-label">温度(°C)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="belt1-tension">850</div>
                            <div class="metric-label">张力(N)</div>
                        </div>
                    </div>
                    <div class="health-score">
                        <span style="font-size: 12px;">健康度:</span>
                        <div class="health-bar">
                            <div class="health-fill health-fair" style="width: 68%;"></div>
                        </div>
                        <span style="font-size: 12px; font-weight: 600;">68%</span>
                    </div>
                </div>

                <div class="equipment-card" onclick="selectEquipment('crusher1')">
                    <div class="equipment-header">
                        <div class="equipment-icon equipment-status-running">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="status-indicator status-normal">
                            <i class="fas fa-check-circle"></i>
                            运行
                        </span>
                    </div>
                    <div class="equipment-name">破碎机1号</div>
                    <div class="equipment-type">颚式破碎机</div>
                    <div class="equipment-metrics">
                        <div class="metric-item">
                            <div class="metric-value" id="crusher1-speed">300</div>
                            <div class="metric-label">转速(rpm)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="crusher1-power">120</div>
                            <div class="metric-label">功率(kW)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="crusher1-pressure">2.5</div>
                            <div class="metric-label">压力(MPa)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="crusher1-output">45</div>
                            <div class="metric-label">产量(t/h)</div>
                        </div>
                    </div>
                    <div class="health-score">
                        <span style="font-size: 12px;">健康度:</span>
                        <div class="health-bar">
                            <div class="health-fill health-good" style="width: 85%;"></div>
                        </div>
                        <span style="font-size: 12px; font-weight: 600;">85%</span>
                    </div>
                </div>

                <div class="equipment-card" onclick="selectEquipment('pump1')">
                    <div class="equipment-header">
                        <div class="equipment-icon equipment-status-fault">
                            <i class="fas fa-tint"></i>
                        </div>
                        <span class="status-indicator status-danger">
                            <i class="fas fa-times-circle"></i>
                            故障
                        </span>
                    </div>
                    <div class="equipment-name">排水泵4号</div>
                    <div class="equipment-type">离心式水泵</div>
                    <div class="equipment-metrics">
                        <div class="metric-item">
                            <div class="metric-value">0</div>
                            <div class="metric-label">流量(m³/h)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">0</div>
                            <div class="metric-label">扬程(m)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">85</div>
                            <div class="metric-label">温度(°C)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">8.5</div>
                            <div class="metric-label">振动(mm/s)</div>
                        </div>
                    </div>
                    <div class="health-score">
                        <span style="font-size: 12px;">健康度:</span>
                        <div class="health-bar">
                            <div class="health-fill health-poor" style="width: 25%;"></div>
                        </div>
                        <span style="font-size: 12px; font-weight: 600;">25%</span>
                    </div>
                </div>
            </div>

            <!-- 设备详细信息 -->
            <div class="equipment-detail">
                <div class="detail-panel">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-chart-line"></i>
                        实时监测数据 - 主通风机1号
                    </h3>
                    <div class="sensor-grid">
                        <div class="sensor-card">
                            <div class="sensor-value" id="detail-temp">45.2°C</div>
                            <div class="sensor-label">轴承温度</div>
                        </div>
                        <div class="sensor-card">
                            <div class="sensor-value" id="detail-vibration">2.1mm/s</div>
                            <div class="sensor-label">振动烈度</div>
                        </div>
                        <div class="sensor-card">
                            <div class="sensor-value" id="detail-current">125A</div>
                            <div class="sensor-label">电流</div>
                        </div>
                        <div class="sensor-card">
                            <div class="sensor-value" id="detail-voltage">380V</div>
                            <div class="sensor-label">电压</div>
                        </div>
                        <div class="sensor-card">
                            <div class="sensor-value" id="detail-power">85kW</div>
                            <div class="sensor-label">功率</div>
                        </div>
                        <div class="sensor-card">
                            <div class="sensor-value" id="detail-efficiency">87%</div>
                            <div class="sensor-label">效率</div>
                        </div>
                    </div>
                    <div class="trend-chart">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area" style="font-size: 48px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                </div>

                <div class="detail-panel">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        设备报警
                    </h3>
                    <div class="alert-list">
                        <div class="alert-item alert-critical">
                            <i class="fas fa-times-circle"></i>
                            <div>
                                <strong>4号排水泵轴承故障</strong><br>
                                <small>轴承温度85°C，振动8.5mm/s</small>
                            </div>
                        </div>
                        <div class="alert-item alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div>
                                <strong>2号皮带机温度偏高</strong><br>
                                <small>滚筒温度68°C，超过正常范围</small>
                            </div>
                        </div>
                        <div class="alert-item alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div>
                                <strong>1号通风机效率下降</strong><br>
                                <small>运行效率87%，低于设计值</small>
                            </div>
                        </div>
                        <div class="alert-item alert-info">
                            <i class="fas fa-info-circle"></i>
                            <div>
                                <strong>定期维护提醒</strong><br>
                                <small>1号破碎机需要进行润滑保养</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI预测分析 -->
            <div class="predictive-analysis">
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="analysis-title">异常检测分析</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>检测模型：</strong>孤立森林算法</p>
                        <p><strong>监测参数：</strong>温度、振动、电流、功率</p>
                        <p><strong>异常设备：</strong>2号皮带机、4号排水泵</p>
                        <p><strong>异常概率：</strong>皮带机 65%，排水泵 95%</p>
                        <p><strong>建议措施：</strong>立即检修排水泵，监控皮带机</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="analysis-title">趋势预测</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>预测模型：</strong>LSTM时序预测</p>
                        <p><strong>预测周期：</strong>未来7天</p>
                        <p><strong>关键预测：</strong></p>
                        <p>• 1号通风机效率将持续下降</p>
                        <p>• 2号皮带机温度将在3天内达到临界值</p>
                        <p>• 1号破碎机运行状态良好</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="analysis-title">维护建议</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>智能维护策略：</strong></p>
                        <p><strong>紧急维护：</strong></p>
                        <p>• 4号排水泵 - 更换轴承（2小时）</p>
                        <p><strong>计划维护：</strong></p>
                        <p>• 2号皮带机 - 清洁散热器（明天）</p>
                        <p>• 1号通风机 - 叶轮平衡校正（本周）</p>
                        <p>• 1号破碎机 - 润滑保养（下周）</p>
                    </div>
                </div>
            </div>

            <!-- 维护计划 -->
            <div class="maintenance-schedule">
                <h3 class="card-title mb-3">
                    <i class="fas fa-calendar-alt"></i>
                    维护计划安排
                </h3>
                <div class="schedule-item">
                    <div class="schedule-info">
                        <div class="schedule-equipment">4号排水泵</div>
                        <div class="schedule-task">紧急维修 - 轴承更换</div>
                    </div>
                    <div class="schedule-time">
                        <div style="color: var(--danger-color); font-weight: 600;">立即执行</div>
                        <div>预计2小时</div>
                    </div>
                </div>
                <div class="schedule-item">
                    <div class="schedule-info">
                        <div class="schedule-equipment">2号皮带机</div>
                        <div class="schedule-task">散热器清洁维护</div>
                    </div>
                    <div class="schedule-time">
                        <div style="color: var(--warning-color); font-weight: 600;">明天 08:00</div>
                        <div>预计1小时</div>
                    </div>
                </div>
                <div class="schedule-item">
                    <div class="schedule-info">
                        <div class="schedule-equipment">1号通风机</div>
                        <div class="schedule-task">叶轮平衡校正</div>
                    </div>
                    <div class="schedule-time">
                        <div style="color: var(--info-color); font-weight: 600;">本周五 14:00</div>
                        <div>预计4小时</div>
                    </div>
                </div>
                <div class="schedule-item">
                    <div class="schedule-info">
                        <div class="schedule-equipment">1号破碎机</div>
                        <div class="schedule-task">定期润滑保养</div>
                    </div>
                    <div class="schedule-time">
                        <div style="color: var(--success-color); font-weight: 600;">下周一 10:00</div>
                        <div>预计2小时</div>
                    </div>
                </div>
                <div class="schedule-item">
                    <div class="schedule-info">
                        <div class="schedule-equipment">3号通风机</div>
                        <div class="schedule-task">电机检查维护</div>
                    </div>
                    <div class="schedule-time">
                        <div style="color: var(--success-color); font-weight: 600;">下周三 09:00</div>
                        <div>预计3小时</div>
                    </div>
                </div>
            </div>
        `;

        // 设备选择功能
        function selectEquipment(equipmentId) {
            MiningAI.showNotification(`已选择设备: ${equipmentId}`, 'info');
            // 这里可以添加更多的设备详情显示逻辑
        }

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('fan1-speed', 1400, 1500, 'rpm', 0);
        MiningAI.simulateRealTimeData('fan1-power', 80, 90, 'kW', 0);
        MiningAI.simulateRealTimeData('fan1-temp', 40, 50, '°C', 0);
        MiningAI.simulateRealTimeData('fan1-vibration', 1.8, 2.5, 'mm/s', 1);

        MiningAI.simulateRealTimeData('belt1-speed', 1.0, 1.5, 'm/s', 1);
        MiningAI.simulateRealTimeData('belt1-load', 70, 80, '%', 0);
        MiningAI.simulateRealTimeData('belt1-temp', 65, 75, '°C', 0);
        MiningAI.simulateRealTimeData('belt1-tension', 800, 900, 'N', 0);

        MiningAI.simulateRealTimeData('crusher1-speed', 290, 310, 'rpm', 0);
        MiningAI.simulateRealTimeData('crusher1-power', 115, 125, 'kW', 0);
        MiningAI.simulateRealTimeData('crusher1-pressure', 2.3, 2.7, 'MPa', 1);
        MiningAI.simulateRealTimeData('crusher1-output', 40, 50, 't/h', 0);

        // 详细监测数据更新
        MiningAI.simulateRealTimeData('detail-temp', 42, 48, '°C', 1);
        MiningAI.simulateRealTimeData('detail-vibration', 1.9, 2.3, 'mm/s', 1);
        MiningAI.simulateRealTimeData('detail-current', 120, 130, 'A', 0);
        MiningAI.simulateRealTimeData('detail-power', 82, 88, 'kW', 0);

        // 将selectEquipment函数添加到全局作用域
        window.selectEquipment = selectEquipment;
    </script>
</body>
</html>
