// 矿业AI管理系统 - 公共JavaScript组件

// 导航菜单配置
const navigationConfig = [
    {
        id: 'dashboard',
        title: '系统首页',
        icon: 'fas fa-tachometer-alt',
        url: 'index.html',
        active: false
    },
    {
        id: 'dust-control',
        title: '粉尘监控',
        icon: 'fas fa-wind',
        url: 'dust-control.html',
        active: false
    },
    {
        id: 'gas-monitoring',
        title: '瓦斯监测',
        icon: 'fas fa-gas-pump',
        url: 'gas-monitoring.html',
        active: false
    },
    {
        id: 'drainage-control',
        title: '排水控制',
        icon: 'fas fa-tint',
        url: 'drainage-control.html',
        active: false
    },
    {
        id: 'equipment-monitoring',
        title: '设备监控',
        icon: 'fas fa-cogs',
        url: 'equipment-monitoring.html',
        active: false
    },
    {
        id: 'personnel-safety',
        title: '人员安全',
        icon: 'fas fa-hard-hat',
        url: 'personnel-safety.html',
        active: false
    },
    {
        id: 'data-platform',
        title: '数据中台',
        icon: 'fas fa-database',
        url: 'data-platform.html',
        active: false
    },
    {
        id: 'operations',
        title: '运维管理',
        icon: 'fas fa-tools',
        url: 'operations.html',
        active: false
    },
    {
        id: 'space-management',
        title: '空间管理',
        icon: 'fas fa-cube',
        url: 'space-management.html',
        active: false
    },
    {
        id: 'inspection-management',
        title: '巡检管理',
        icon: 'fas fa-route',
        url: 'inspection-management.html',
        active: false
    },
    {
        id: 'drone-patrol',
        title: '无人机巡航',
        icon: 'fas fa-helicopter',
        url: 'drone-patrol.html',
        active: false
    }
];

// 生成侧边栏HTML
function generateSidebar(currentPage = '') {
    // 更新当前页面的激活状态
    navigationConfig.forEach(item => {
        item.active = item.url === currentPage;
    });

    const sidebarHTML = `
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-industry"></i>
                    <span>矿业AI管理系统</span>
                </div>
            </div>
            <nav class="nav-menu">
                ${navigationConfig.map(item => `
                    <div class="nav-item">
                        <a href="${item.url}" class="nav-link ${item.active ? 'active' : ''}">
                            <i class="${item.icon}"></i>
                            <span>${item.title}</span>
                        </a>
                    </div>
                `).join('')}
            </nav>
        </div>
    `;
    
    return sidebarHTML;
}

// 生成顶部导航栏HTML
function generateTopNavbar(pageTitle = '系统首页', breadcrumbs = []) {
    const breadcrumbHTML = breadcrumbs.length > 0 ? `
        <div class="breadcrumb">
            ${breadcrumbs.map((crumb, index) => `
                ${index > 0 ? '<span class="breadcrumb-separator">/</span>' : ''}
                <span>${crumb}</span>
            `).join('')}
        </div>
    ` : '';

    const navbarHTML = `
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="btn btn-secondary mobile-menu-toggle" id="mobileMenuToggle" style="display: none;">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="navbar-title">${pageTitle}</div>
                ${breadcrumbHTML}
            </div>
            <div class="navbar-right">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">管理员</div>
                        <div class="user-role">系统管理员</div>
                    </div>
                    <i class="fas fa-chevron-down" style="margin-left: 8px; font-size: 12px;"></i>
                </div>
            </div>
        </div>
    `;
    
    return navbarHTML;
}

// 初始化页面布局
function initializeLayout(currentPage, pageTitle, breadcrumbs = []) {
    // 创建应用容器
    const appContainer = document.createElement('div');
    appContainer.className = 'app-container';
    
    // 生成侧边栏
    appContainer.innerHTML = generateSidebar(currentPage);
    
    // 创建主内容区域
    const mainContent = document.createElement('div');
    mainContent.className = 'main-content';
    mainContent.innerHTML = generateTopNavbar(pageTitle, breadcrumbs);
    
    // 创建页面内容区域
    const pageContent = document.createElement('div');
    pageContent.className = 'page-content';
    pageContent.id = 'pageContent';
    mainContent.appendChild(pageContent);
    
    appContainer.appendChild(mainContent);
    
    // 替换body内容
    document.body.innerHTML = '';
    document.body.appendChild(appContainer);
    
    // 初始化移动端菜单
    initializeMobileMenu();
    
    return pageContent;
}

// 移动端菜单初始化
function initializeMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');
    
    // 检查屏幕尺寸并显示/隐藏移动端菜单按钮
    function checkScreenSize() {
        if (window.innerWidth <= 768) {
            mobileToggle.style.display = 'inline-flex';
        } else {
            mobileToggle.style.display = 'none';
            sidebar.classList.remove('mobile-open');
        }
    }
    
    // 移动端菜单切换
    if (mobileToggle) {
        mobileToggle.addEventListener('click', () => {
            sidebar.classList.toggle('mobile-open');
        });
    }
    
    // 点击页面内容区域关闭移动端菜单
    document.addEventListener('click', (e) => {
        if (window.innerWidth <= 768 && 
            !sidebar.contains(e.target) && 
            !mobileToggle.contains(e.target)) {
            sidebar.classList.remove('mobile-open');
        }
    });
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize);
    checkScreenSize();
}

// 创建状态指示器
function createStatusIndicator(status, text) {
    const statusClasses = {
        'normal': 'status-normal',
        'warning': 'status-warning',
        'danger': 'status-danger'
    };
    
    const statusIcons = {
        'normal': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'danger': 'fas fa-times-circle'
    };
    
    return `
        <span class="status-indicator ${statusClasses[status] || 'status-normal'}">
            <i class="${statusIcons[status] || 'fas fa-check-circle'}"></i>
            ${text}
        </span>
    `;
}

// 创建数据卡片
function createDataCard(title, value, unit = '', trend = null, icon = 'fas fa-chart-line') {
    const trendHTML = trend ? `
        <div class="card-trend ${trend.type}">
            <i class="fas fa-arrow-${trend.type === 'up' ? 'up' : 'down'}"></i>
            ${trend.value}
        </div>
    ` : '';
    
    return `
        <div class="card data-card">
            <div class="card-body">
                <div class="data-card-header">
                    <div class="data-card-icon">
                        <i class="${icon}"></i>
                    </div>
                    <div class="data-card-info">
                        <div class="data-card-title">${title}</div>
                        <div class="data-card-value">${value} <span class="data-card-unit">${unit}</span></div>
                    </div>
                </div>
                ${trendHTML}
            </div>
        </div>
    `;
}

// 格式化时间
function formatDateTime(date) {
    if (!date) date = new Date();
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 生成随机数据（用于演示）
function generateRandomData(min, max, decimals = 0) {
    const value = Math.random() * (max - min) + min;
    return decimals > 0 ? value.toFixed(decimals) : Math.floor(value);
}

// 模拟实时数据更新
function simulateRealTimeData(elementId, min, max, unit = '', decimals = 0) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    setInterval(() => {
        const value = generateRandomData(min, max, decimals);
        element.textContent = `${value} ${unit}`;
    }, 2000 + Math.random() * 3000); // 2-5秒随机更新
}

// 显示通知消息
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation' : type === 'error' ? 'times' : 'info'}-circle"></i>
        <span>${message}</span>
        <button class="notification-close">&times;</button>
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动关闭
    setTimeout(() => {
        notification.remove();
    }, duration);
    
    // 手动关闭
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以在这里添加全局初始化代码
    console.log('矿业AI管理系统已加载');
});

// 导出全局函数
window.MiningAI = {
    initializeLayout,
    createStatusIndicator,
    createDataCard,
    formatDateTime,
    generateRandomData,
    simulateRealTimeData,
    showNotification
};
