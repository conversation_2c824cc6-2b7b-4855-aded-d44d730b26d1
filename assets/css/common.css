/* 矿业AI管理系统 - 公共样式 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量定义 */
:root {
    /* 主色调 */
    --primary-color: #1e3a8a;
    --primary-light: #3b82f6;
    --primary-dark: #1e40af;
    
    /* 功能色彩 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* 中性色 */
    --bg-color: #f8fafc;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* 间距 */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* 布局容器 */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 20px;
    font-weight: 600;
}

.sidebar-logo i {
    font-size: 24px;
    color: var(--info-color);
}

/* 导航菜单 */
.nav-menu {
    padding: var(--spacing-sm) 0;
}

.nav-item {
    margin: 4px var(--spacing-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 12px var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    font-size: 14px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 260px;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-navbar {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0 var(--spacing-md);
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 999;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.navbar-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 14px;
}

.breadcrumb-separator {
    color: var(--text-muted);
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.user-info:hover {
    background: var(--bg-color);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.user-role {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 页面内容区域 */
.page-content {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

/* 卡片组件 */
.card {
    background: var(--card-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-color);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.card-body {
    padding: var(--spacing-md);
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s ease;
    line-height: 1.5;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-secondary {
    background: var(--text-secondary);
    color: white;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
}

.status-normal {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .navbar-title {
        font-size: 16px;
    }
    
    .user-details {
        display: none;
    }
}

/* 数据卡片组件 */
.data-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.data-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.data-card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, var(--primary-light), var(--info-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.data-card-info {
    flex: 1;
}

.data-card-title {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.data-card-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

.data-card-unit {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
}

.card-trend {
    margin-top: var(--spacing-sm);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.card-trend.up {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.card-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* 通知组件 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    z-index: 10000;
    min-width: 300px;
    border-left: 4px solid var(--info-color);
    animation: slideInRight 0.3s ease;
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-muted);
    margin-left: auto;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 网格布局 */
.grid {
    display: grid;
    gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }

/* 新增模块图标样式 */
.icon-space {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
}

.icon-inspection {
    background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
}

.icon-drone {
    background: linear-gradient(135deg, #10b981, #059669) !important;
}
