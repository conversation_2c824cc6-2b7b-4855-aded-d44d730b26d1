<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排水控制 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .drainage-overview {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .water-level-monitor {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .level-display {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .level-gauge {
            width: 80px;
            height: 200px;
            background: linear-gradient(to top, #e2e8f0 0%, #e2e8f0 100%);
            border-radius: 40px;
            position: relative;
            border: 3px solid var(--border-color);
        }

        .level-water {
            position: absolute;
            bottom: 3px;
            left: 3px;
            right: 3px;
            background: linear-gradient(to top, #06b6d4, #0891b2);
            border-radius: 34px;
            transition: height 0.5s ease;
            height: 60%;
        }

        .level-markers {
            position: absolute;
            right: -30px;
            top: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .level-info {
            flex: 1;
        }

        .level-value {
            font-size: 48px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .level-status {
            font-size: 16px;
            margin-bottom: var(--spacing-sm);
        }

        .level-prediction {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 14px;
            color: var(--text-secondary);
        }

        .pump-status-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .pump-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
        }

        .pump-item:hover {
            background: var(--bg-color);
        }

        .pump-item:last-child {
            border-bottom: none;
        }

        .pump-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .pump-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .pump-running {
            background: var(--success-color);
            animation: pump-pulse 1.5s ease-in-out infinite;
        }

        .pump-stopped {
            background: var(--text-muted);
        }

        .pump-fault {
            background: var(--danger-color);
        }

        @keyframes pump-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .pump-details {
            flex: 1;
        }

        .pump-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .pump-specs {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .pump-controls {
            display: flex;
            gap: 4px;
        }

        .pump-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .pump-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .control-strategy {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .strategy-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .strategy-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .strategy-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .strategy-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .strategy-content {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        .efficiency-chart {
            height: 150px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .maintenance-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .maintenance-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .maintenance-item:last-child {
            margin-bottom: 0;
        }

        .maintenance-urgent {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }

        .maintenance-scheduled {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning-color);
        }

        .maintenance-completed {
            background: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success-color);
        }

        .maintenance-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .maintenance-content {
            flex: 1;
        }

        .maintenance-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 14px;
        }

        .maintenance-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .alert-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-top: var(--spacing-md);
        }

        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .parameter-item {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            text-align: center;
        }

        .parameter-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .parameter-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .parameter-unit {
            font-size: 12px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('drainage-control.html', '排水控制', ['首页', '排水控制']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 排水系统概览 -->
            <div class="drainage-overview">
                <!-- 水位监测 -->
                <div class="water-level-monitor">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-tint"></i>
                        实时水位监测
                    </h3>
                    <div class="level-display">
                        <div class="level-gauge">
                            <div class="level-water" id="waterLevel"></div>
                            <div class="level-markers">
                                <span>5.0m</span>
                                <span>4.0m</span>
                                <span>3.0m</span>
                                <span>2.0m</span>
                                <span>1.0m</span>
                                <span>0.0m</span>
                            </div>
                        </div>
                        <div class="level-info">
                            <div class="level-value" id="currentLevel">2.3m</div>
                            <div class="level-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-check-circle"></i>
                                    正常水位
                                </span>
                            </div>
                            <div class="level-prediction">
                                <strong>LSTM预测：</strong><br>
                                未来2小时水位将上升至2.8m<br>
                                预计启动时间：45分钟后<br>
                                建议启动2号水泵
                            </div>
                        </div>
                    </div>
                    
                    <!-- 关键参数 -->
                    <div class="parameter-grid">
                        <div class="parameter-item">
                            <div class="parameter-label">流入量</div>
                            <div class="parameter-value" id="inflowRate">125 <span class="parameter-unit">L/min</span></div>
                        </div>
                        <div class="parameter-item">
                            <div class="parameter-label">排出量</div>
                            <div class="parameter-value" id="outflowRate">98 <span class="parameter-unit">L/min</span></div>
                        </div>
                        <div class="parameter-item">
                            <div class="parameter-label">水温</div>
                            <div class="parameter-value" id="waterTemp">18.5 <span class="parameter-unit">°C</span></div>
                        </div>
                        <div class="parameter-item">
                            <div class="parameter-label">pH值</div>
                            <div class="parameter-value" id="phValue">7.2 <span class="parameter-unit"></span></div>
                        </div>
                    </div>
                </div>

                <!-- 水泵状态面板 -->
                <div class="pump-status-panel">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-cogs"></i>
                        水泵运行状态
                    </h3>
                    <div class="pump-item">
                        <div class="pump-info">
                            <div class="pump-icon pump-running">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="pump-details">
                                <div class="pump-name">1号主泵</div>
                                <div class="pump-specs">功率: 75kW | 流量: 180m³/h</div>
                            </div>
                        </div>
                        <div class="pump-controls">
                            <button class="pump-btn">停止</button>
                            <button class="pump-btn">调速</button>
                        </div>
                    </div>
                    <div class="pump-item">
                        <div class="pump-info">
                            <div class="pump-icon pump-stopped">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="pump-details">
                                <div class="pump-name">2号主泵</div>
                                <div class="pump-specs">功率: 75kW | 流量: 180m³/h</div>
                            </div>
                        </div>
                        <div class="pump-controls">
                            <button class="pump-btn">启动</button>
                            <button class="pump-btn">调速</button>
                        </div>
                    </div>
                    <div class="pump-item">
                        <div class="pump-info">
                            <div class="pump-icon pump-running">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="pump-details">
                                <div class="pump-name">3号备用泵</div>
                                <div class="pump-specs">功率: 55kW | 流量: 120m³/h</div>
                            </div>
                        </div>
                        <div class="pump-controls">
                            <button class="pump-btn">停止</button>
                            <button class="pump-btn">调速</button>
                        </div>
                    </div>
                    <div class="pump-item">
                        <div class="pump-info">
                            <div class="pump-icon pump-fault">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="pump-details">
                                <div class="pump-name">4号备用泵</div>
                                <div class="pump-specs">功率: 55kW | 故障状态</div>
                            </div>
                        </div>
                        <div class="pump-controls">
                            <button class="pump-btn">检修</button>
                            <button class="pump-btn">复位</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能控制策略 -->
            <div class="control-strategy">
                <div class="strategy-card">
                    <div class="strategy-header">
                        <div class="strategy-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="strategy-title">智能启停控制</div>
                    </div>
                    <div class="strategy-content">
                        <p><strong>当前策略：</strong>模拟退火算法优化</p>
                        <p><strong>启动阈值：</strong>水位 ≥ 3.0m</p>
                        <p><strong>停止阈值：</strong>水位 ≤ 1.5m</p>
                        <p><strong>轮换周期：</strong>每8小时自动轮换</p>
                        <p><strong>节能模式：</strong>已启用</p>
                    </div>
                </div>

                <div class="strategy-card">
                    <div class="strategy-header">
                        <div class="strategy-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="strategy-title">能效分析</div>
                    </div>
                    <div class="efficiency-chart">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area" style="font-size: 32px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                    <div class="strategy-content">
                        <p><strong>当前效率：</strong>87.5%</p>
                        <p><strong>日均耗电：</strong>1,250 kWh</p>
                        <p><strong>节能比例：</strong>15.2%</p>
                    </div>
                </div>

                <div class="strategy-card">
                    <div class="strategy-header">
                        <div class="strategy-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="strategy-title">故障诊断</div>
                    </div>
                    <div class="strategy-content">
                        <p><strong>诊断模型：</strong>多元异常检测</p>
                        <p><strong>监测参数：</strong>振动、温度、电流</p>
                        <p><strong>预警等级：</strong>正常</p>
                        <p><strong>下次检修：</strong>7天后</p>
                        <p><strong>健康评分：</strong>92/100</p>
                    </div>
                </div>
            </div>

            <!-- 预测性维护 -->
            <div class="maintenance-panel">
                <h3 class="card-title mb-3">
                    <i class="fas fa-wrench"></i>
                    预测性维护计划
                </h3>
                <div class="maintenance-item maintenance-urgent">
                    <div class="maintenance-icon" style="background: var(--danger-color); color: white;">
                        <i class="fas fa-exclamation"></i>
                    </div>
                    <div class="maintenance-content">
                        <div class="maintenance-title">4号水泵轴承异常</div>
                        <div class="maintenance-time">紧急维修 | 预计停机2小时</div>
                    </div>
                </div>
                <div class="maintenance-item maintenance-scheduled">
                    <div class="maintenance-icon" style="background: var(--warning-color); color: white;">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="maintenance-content">
                        <div class="maintenance-title">1号水泵定期保养</div>
                        <div class="maintenance-time">计划维护 | 3天后执行</div>
                    </div>
                </div>
                <div class="maintenance-item maintenance-scheduled">
                    <div class="maintenance-icon" style="background: var(--warning-color); color: white;">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="maintenance-content">
                        <div class="maintenance-title">叶轮清洗维护</div>
                        <div class="maintenance-time">计划维护 | 1周后执行</div>
                    </div>
                </div>
                <div class="maintenance-item maintenance-completed">
                    <div class="maintenance-icon" style="background: var(--success-color); color: white;">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="maintenance-content">
                        <div class="maintenance-title">2号水泵电机检修</div>
                        <div class="maintenance-time">已完成 | 昨天完成</div>
                    </div>
                </div>
            </div>

            <!-- 报警和事件记录 -->
            <div class="alert-panel">
                <h3 class="card-title mb-3">
                    <i class="fas fa-bell"></i>
                    实时报警与事件记录
                </h3>
                <div class="alert-item alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <div>
                        <strong>水泵故障报警</strong> - 4号备用泵轴承温度过高，已自动停机
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            5分钟前 | 已通知维修人员
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>水位上升预警</strong> - 预测2小时后水位将达到启动阈值
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            10分钟前 | AI预测系统
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-normal">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>水泵轮换执行</strong> - 1号主泵与3号备用泵完成自动轮换
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            2小时前 | 系统自动执行
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-normal">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>系统自检完成</strong> - 所有传感器和控制系统运行正常
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            6小时前 | 定期检查
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 水位显示更新
        function updateWaterLevel(level) {
            const waterElement = document.getElementById('waterLevel');
            const levelElement = document.getElementById('currentLevel');
            const percentage = (level / 5.0) * 100; // 假设最大水位5米
            waterElement.style.height = percentage + '%';
            levelElement.textContent = level.toFixed(1) + 'm';
        }

        // 水泵控制按钮事件
        document.querySelectorAll('.pump-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.textContent;
                const pumpName = e.target.closest('.pump-item').querySelector('.pump-name').textContent;
                MiningAI.showNotification(`${pumpName} ${action}操作已执行`, 'info');
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('inflowRate', 100, 150, 'L/min', 0);
        MiningAI.simulateRealTimeData('outflowRate', 80, 120, 'L/min', 0);
        MiningAI.simulateRealTimeData('waterTemp', 15, 25, '°C', 1);
        MiningAI.simulateRealTimeData('phValue', 6.8, 7.5, '', 1);

        // 模拟水位变化
        setInterval(() => {
            const level = MiningAI.generateRandomData(2.0, 3.5, 1);
            updateWaterLevel(level);
        }, 3000);

        // 初始化水位
        updateWaterLevel(2.3);
    </script>
</body>
</html>
