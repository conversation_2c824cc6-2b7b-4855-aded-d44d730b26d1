<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空间管理 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .space-overview {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .space-viewer {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .viewer-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .view-mode-tabs {
            display: flex;
            gap: 4px;
        }

        .tab-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .viewer-tools {
            display: flex;
            gap: 4px;
        }

        .tool-btn {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .tool-btn:hover {
            background: var(--bg-color);
        }

        .space-canvas {
            height: 500px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
            border: 2px solid var(--border-color);
        }

        .space-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .space-zone {
            position: absolute;
            border: 2px solid;
            border-radius: var(--radius-sm);
            background: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .space-zone:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-lg);
        }

        .zone-production {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .zone-transport {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .zone-maintenance {
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .zone-restricted {
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        .equipment-marker {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            cursor: pointer;
            animation: pulse 2s infinite;
        }

        .equipment-running {
            background: var(--success-color);
        }

        .equipment-warning {
            background: var(--warning-color);
        }

        .equipment-fault {
            background: var(--danger-color);
        }

        .personnel-dot {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--info-color);
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
        }

        .zone-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .zone-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .zone-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
        }

        .zone-item:hover {
            background: var(--bg-color);
        }

        .zone-item:last-child {
            border-bottom: none;
        }

        .zone-info {
            flex: 1;
        }

        .zone-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .zone-details {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .zone-status {
            text-align: right;
        }

        .zone-capacity {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .permission-management {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .permission-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-user {
            font-weight: 500;
            color: var(--text-primary);
        }

        .permission-level {
            padding: 2px 6px;
            border-radius: var(--radius-sm);
            font-size: 10px;
            font-weight: 500;
        }

        .level-admin {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .level-operator {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .level-viewer {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .alert-management {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .alert-item:last-child {
            margin-bottom: 0;
        }

        .alert-critical {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning-color);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            border-left: 4px solid var(--info-color);
        }

        .space-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
            display: none;
            max-width: 200px;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .stat-item {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .approval-workflow {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-top: var(--spacing-md);
        }

        .workflow-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .workflow-item:last-child {
            border-bottom: none;
        }

        .workflow-info {
            flex: 1;
        }

        .workflow-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 13px;
        }

        .workflow-details {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .workflow-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-approved {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-rejected {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('space-management.html', '空间管理', ['首页', '空间管理']);

        // 页面内容
        pageContent.innerHTML = `
            <!-- 空间管理概览 -->
            <div class="space-overview">
                <!-- 空间视图器 -->
                <div class="space-viewer">
                    <div class="viewer-controls">
                        <div class="view-mode-tabs">
                            <button class="tab-btn active" data-mode="2d">2D视图</button>
                            <button class="tab-btn" data-mode="3d">3D视图</button>
                            <button class="tab-btn" data-mode="layout">设备布局</button>
                        </div>
                        <div class="viewer-tools">
                            <button class="tool-btn" title="放大">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="tool-btn" title="缩小">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button class="tool-btn" title="重置视图">
                                <i class="fas fa-home"></i>
                            </button>
                            <button class="tool-btn" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>

                    <div class="space-canvas" id="spaceCanvas">
                        <div class="space-grid"></div>

                        <!-- 作业区域 -->
                        <div class="space-zone zone-production" style="top: 10%; left: 10%; width: 35%; height: 25%;"
                             data-zone="production-1" data-name="1号生产区" data-capacity="50" data-current="32">
                            1号生产区
                        </div>
                        <div class="space-zone zone-production" style="top: 10%; right: 10%; width: 35%; height: 25%;"
                             data-zone="production-2" data-name="2号生产区" data-capacity="45" data-current="28">
                            2号生产区
                        </div>
                        <div class="space-zone zone-transport" style="top: 45%; left: 10%; width: 80%; height: 15%;"
                             data-zone="transport-main" data-name="主运输巷道" data-capacity="30" data-current="15">
                            主运输巷道
                        </div>
                        <div class="space-zone zone-maintenance" style="top: 70%; left: 10%; width: 25%; height: 20%;"
                             data-zone="maintenance" data-name="维修车间" data-capacity="20" data-current="8">
                            维修车间
                        </div>
                        <div class="space-zone zone-restricted" style="top: 70%; right: 10%; width: 25%; height: 20%;"
                             data-zone="restricted" data-name="危险品存储区" data-capacity="5" data-current="2">
                            危险品存储区
                        </div>

                        <!-- 设备标记 -->
                        <div class="equipment-marker equipment-running" style="top: 20%; left: 25%;"
                             data-equipment="fan-01" data-name="主通风机1号" data-status="运行中">
                            <i class="fas fa-fan"></i>
                        </div>
                        <div class="equipment-marker equipment-running" style="top: 25%; right: 25%;"
                             data-equipment="belt-01" data-name="皮带机1号" data-status="运行中">
                            <i class="fas fa-conveyor-belt"></i>
                        </div>
                        <div class="equipment-marker equipment-warning" style="top: 50%; left: 50%;"
                             data-equipment="pump-01" data-name="排水泵1号" data-status="维护中">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="equipment-marker equipment-fault" style="top: 75%; left: 20%;"
                             data-equipment="crane-01" data-name="起重机1号" data-status="故障">
                            <i class="fas fa-tools"></i>
                        </div>

                        <!-- 人员位置点 -->
                        <div class="personnel-dot" style="top: 15%; left: 20%;" data-person="张三" data-role="班长"></div>
                        <div class="personnel-dot" style="top: 18%; right: 30%;" data-person="李四" data-role="操作员"></div>
                        <div class="personnel-dot" style="top: 48%; left: 45%;" data-person="王五" data-role="维修工"></div>
                        <div class="personnel-dot" style="top: 52%; left: 55%;" data-person="赵六" data-role="安全员"></div>
                        <div class="personnel-dot" style="top: 75%; left: 25%;" data-person="孙七" data-role="技术员"></div>

                        <div class="space-tooltip" id="spaceTooltip"></div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="statistics-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="totalPersonnel">127</div>
                            <div class="stat-label">在线人员</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalZones">12</div>
                            <div class="stat-label">管理区域</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalEquipment">42</div>
                            <div class="stat-label">设备数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="alertCount">3</div>
                            <div class="stat-label">活跃报警</div>
                        </div>
                    </div>
                </div>

                <!-- 区域管理面板 -->
                <div class="zone-panel">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-layer-group"></i>
                        区域管理
                    </h3>
                    <div class="zone-list">
                        <div class="zone-item">
                            <div class="zone-info">
                                <div class="zone-name">1号生产区</div>
                                <div class="zone-details">面积: 1200m² | 类型: 生产作业</div>
                            </div>
                            <div class="zone-status">
                                <span class="status-indicator status-warning">
                                    <i class="fas fa-users"></i>
                                    32/50
                                </span>
                                <div class="zone-capacity">64%容量</div>
                            </div>
                        </div>
                        <div class="zone-item">
                            <div class="zone-info">
                                <div class="zone-name">2号生产区</div>
                                <div class="zone-details">面积: 1000m² | 类型: 生产作业</div>
                            </div>
                            <div class="zone-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-users"></i>
                                    28/45
                                </span>
                                <div class="zone-capacity">62%容量</div>
                            </div>
                        </div>
                        <div class="zone-item">
                            <div class="zone-info">
                                <div class="zone-name">主运输巷道</div>
                                <div class="zone-details">长度: 800m | 类型: 运输通道</div>
                            </div>
                            <div class="zone-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-users"></i>
                                    15/30
                                </span>
                                <div class="zone-capacity">50%容量</div>
                            </div>
                        </div>
                        <div class="zone-item">
                            <div class="zone-info">
                                <div class="zone-name">维修车间</div>
                                <div class="zone-details">面积: 400m² | 类型: 维修保养</div>
                            </div>
                            <div class="zone-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-users"></i>
                                    8/20
                                </span>
                                <div class="zone-capacity">40%容量</div>
                            </div>
                        </div>
                        <div class="zone-item">
                            <div class="zone-info">
                                <div class="zone-name">危险品存储区</div>
                                <div class="zone-details">面积: 200m² | 类型: 限制区域</div>
                            </div>
                            <div class="zone-status">
                                <span class="status-indicator status-danger">
                                    <i class="fas fa-users"></i>
                                    2/5
                                </span>
                                <div class="zone-capacity">40%容量</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限管理 -->
            <div class="permission-management">
                <div class="permission-card">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-key"></i>
                        区域权限设置
                    </h3>
                    <div class="permission-item">
                        <span class="permission-user">张三 (班长)</span>
                        <span class="permission-level level-admin">管理员</span>
                    </div>
                    <div class="permission-item">
                        <span class="permission-user">李四 (操作员)</span>
                        <span class="permission-level level-operator">操作员</span>
                    </div>
                    <div class="permission-item">
                        <span class="permission-user">王五 (维修工)</span>
                        <span class="permission-level level-operator">操作员</span>
                    </div>
                    <div class="permission-item">
                        <span class="permission-user">赵六 (安全员)</span>
                        <span class="permission-level level-admin">管理员</span>
                    </div>
                    <div class="permission-item">
                        <span class="permission-user">孙七 (技术员)</span>
                        <span class="permission-level level-viewer">查看者</span>
                    </div>
                </div>

                <div class="permission-card">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-shield-alt"></i>
                        访问控制规则
                    </h3>
                    <div style="font-size: 12px; color: var(--text-secondary); line-height: 1.5;">
                        <p><strong>生产区域：</strong></p>
                        <p>• 需要生产作业证书</p>
                        <p>• 最大容量限制</p>
                        <p>• 安全装备检查</p>
                        <br>
                        <p><strong>危险区域：</strong></p>
                        <p>• 需要特殊授权</p>
                        <p>• 双人作业制度</p>
                        <p>• 实时监控必须</p>
                        <br>
                        <p><strong>维修区域：</strong></p>
                        <p>• 维修资质要求</p>
                        <p>• 设备停机确认</p>
                        <p>• 安全隔离措施</p>
                    </div>
                </div>
            </div>

            <!-- 作业审批工作流 -->
            <div class="approval-workflow">
                <h3 class="card-title mb-3">
                    <i class="fas fa-clipboard-check"></i>
                    作业审批工作流
                </h3>
                <div class="workflow-item">
                    <div class="workflow-info">
                        <div class="workflow-title">危险品存储区检修申请</div>
                        <div class="workflow-details">申请人: 王五 | 申请时间: 2024-01-15 14:30 | 预计时长: 2小时</div>
                    </div>
                    <div class="workflow-status status-pending">待审批</div>
                </div>
                <div class="workflow-item">
                    <div class="workflow-info">
                        <div class="workflow-title">1号生产区设备维护</div>
                        <div class="workflow-details">申请人: 李四 | 申请时间: 2024-01-15 10:15 | 预计时长: 4小时</div>
                    </div>
                    <div class="workflow-status status-approved">已批准</div>
                </div>
                <div class="workflow-item">
                    <div class="workflow-info">
                        <div class="workflow-title">主巷道清洁作业</div>
                        <div class="workflow-details">申请人: 孙七 | 申请时间: 2024-01-15 08:45 | 预计时长: 1小时</div>
                    </div>
                    <div class="workflow-status status-approved">已批准</div>
                </div>
                <div class="workflow-item">
                    <div class="workflow-info">
                        <div class="workflow-title">2号生产区夜班作业</div>
                        <div class="workflow-details">申请人: 赵六 | 申请时间: 2024-01-14 16:20 | 预计时长: 8小时</div>
                    </div>
                    <div class="workflow-status status-rejected">已拒绝</div>
                </div>
            </div>

            <!-- 超员预警和报警管理 -->
            <div class="alert-management">
                <h3 class="card-title mb-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    超员预警与安全报警
                </h3>
                <div class="alert-item alert-warning">
                    <i class="fas fa-users"></i>
                    <div>
                        <strong>1号生产区人员接近上限</strong> - 当前32人，容量50人，使用率64%
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            5分钟前 | 建议控制新增人员进入
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-critical">
                    <i class="fas fa-ban"></i>
                    <div>
                        <strong>危险品存储区未授权进入</strong> - 检测到无权限人员尝试进入
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            2分钟前 | 已阻止进入并通知安全员
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-info">
                    <i class="fas fa-clock"></i>
                    <div>
                        <strong>维修车间作业超时提醒</strong> - 王五在维修车间作业已超过预定时间
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            10分钟前 | 已发送提醒通知
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-info">
                    <i class="fas fa-route"></i>
                    <div>
                        <strong>人员轨迹异常</strong> - 李四在非授权区域停留时间过长
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            15分钟前 | 正在核实情况
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 视图模式切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除其他按钮的active状态
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                const mode = e.target.dataset.mode;
                MiningAI.showNotification(`切换到${mode.toUpperCase()}视图`, 'info');
            });
        });

        // 工具按钮事件
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const title = e.target.closest('.tool-btn').title;
                MiningAI.showNotification(`${title}功能已激活`, 'info');
            });
        });

        // 区域悬停提示
        const spaceElements = document.querySelectorAll('.space-zone, .equipment-marker, .personnel-dot');
        const tooltip = document.getElementById('spaceTooltip');

        spaceElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                let tooltipContent = '';

                if (element.classList.contains('space-zone')) {
                    const name = e.target.dataset.name;
                    const capacity = e.target.dataset.capacity;
                    const current = e.target.dataset.current;
                    const usage = Math.round((current / capacity) * 100);

                    tooltipContent = `
                        <strong>${name}</strong><br>
                        当前人数: ${current}人<br>
                        最大容量: ${capacity}人<br>
                        使用率: ${usage}%
                    `;
                } else if (element.classList.contains('equipment-marker')) {
                    const name = e.target.dataset.name;
                    const status = e.target.dataset.status;

                    tooltipContent = `
                        <strong>${name}</strong><br>
                        状态: ${status}
                    `;
                } else if (element.classList.contains('personnel-dot')) {
                    const person = e.target.dataset.person;
                    const role = e.target.dataset.role;

                    tooltipContent = `
                        <strong>${person}</strong><br>
                        职位: ${role}
                    `;
                }

                tooltip.innerHTML = tooltipContent;
                tooltip.style.display = 'block';
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            });

            element.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });

            element.addEventListener('click', (e) => {
                if (element.classList.contains('space-zone')) {
                    const name = e.target.dataset.name;
                    MiningAI.showNotification(`已选择区域: ${name}`, 'info');
                } else if (element.classList.contains('equipment-marker')) {
                    const name = e.target.dataset.name;
                    MiningAI.showNotification(`已选择设备: ${name}`, 'info');
                } else if (element.classList.contains('personnel-dot')) {
                    const person = e.target.dataset.person;
                    MiningAI.showNotification(`已选择人员: ${person}`, 'info');
                }
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('totalPersonnel', 120, 135, '人', 0);
        MiningAI.simulateRealTimeData('totalZones', 11, 13, '个', 0);
        MiningAI.simulateRealTimeData('totalEquipment', 40, 45, '台', 0);
        MiningAI.simulateRealTimeData('alertCount', 2, 5, '个', 0);

        // 模拟人员位置更新
        setInterval(() => {
            const personnelDots = document.querySelectorAll('.personnel-dot');
            personnelDots.forEach(dot => {
                // 随机微调位置，模拟人员移动
                const currentTop = parseFloat(dot.style.top);
                const currentLeft = parseFloat(dot.style.left);

                const newTop = currentTop + (Math.random() - 0.5) * 3;
                const newLeft = currentLeft + (Math.random() - 0.5) * 3;

                // 确保不超出边界
                if (newTop > 5 && newTop < 85) {
                    dot.style.top = newTop + '%';
                }
                if (newLeft > 5 && newLeft < 85) {
                    dot.style.left = newLeft + '%';
                }
            });
        }, 8000);

        // 模拟区域人数变化
        setInterval(() => {
            const zones = document.querySelectorAll('.space-zone');
            zones.forEach(zone => {
                const capacity = parseInt(zone.dataset.capacity);
                const current = parseInt(zone.dataset.current);
                const change = Math.floor(Math.random() * 5) - 2; // -2 到 +2 的变化
                const newCurrent = Math.max(0, Math.min(capacity, current + change));

                zone.dataset.current = newCurrent;

                // 更新对应的区域列表显示
                const zoneName = zone.dataset.name;
                const zoneItems = document.querySelectorAll('.zone-item');
                zoneItems.forEach(item => {
                    if (item.querySelector('.zone-name').textContent === zoneName) {
                        const statusSpan = item.querySelector('.status-indicator');
                        const capacityDiv = item.querySelector('.zone-capacity');
                        const usage = Math.round((newCurrent / capacity) * 100);

                        statusSpan.innerHTML = `<i class="fas fa-users"></i> ${newCurrent}/${capacity}`;
                        capacityDiv.textContent = `${usage}%容量`;

                        // 更新状态颜色
                        statusSpan.className = 'status-indicator';
                        if (usage > 80) {
                            statusSpan.classList.add('status-danger');
                        } else if (usage > 60) {
                            statusSpan.classList.add('status-warning');
                        } else {
                            statusSpan.classList.add('status-normal');
                        }
                    }
                });
            });
        }, 10000);

        // 显示系统启动消息
        setTimeout(() => {
            MiningAI.showNotification('空间管理系统已加载', 'success');
        }, 1000);
    </script>
</body>
</html>
