# 矿业AI管理系统部署指南

## 📋 部署概述

本系统采用前后端分离架构，前端为纯静态HTML页面，可部署在任何Web服务器上。后端需要根据实际业务需求集成相应的数据源和AI服务。

## 🏗️ 系统架构

### 前端架构
```
浏览器 → Web服务器 → 静态HTML/CSS/JS文件
```

### 完整系统架构
```
传感器设备 → 数据采集网关 → 边缘计算节点 → 云端服务 → Web前端
```

## 🚀 前端部署

### 1. 静态文件部署

#### Nginx部署
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/mining-ai;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

#### Apache部署
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/mining-ai
    
    <Directory /var/www/mining-ai>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>
</VirtualHost>
```

### 2. Docker部署
```dockerfile
FROM nginx:alpine

# 复制静态文件
COPY . /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

构建和运行：
```bash
docker build -t mining-ai-frontend .
docker run -d -p 80:80 mining-ai-frontend
```

### 3. CDN部署
将静态文件上传到CDN服务商（如阿里云OSS、腾讯云COS、AWS S3等），配置静态网站托管。

## 🔧 后端集成

### 1. 数据接口集成

修改 `assets/js/common.js` 中的数据模拟函数，替换为真实API调用：

```javascript
// 替换模拟数据函数
function fetchRealTimeData(endpoint, elementId, unit) {
    fetch(`/api/${endpoint}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById(elementId).textContent = `${data.value} ${unit}`;
        })
        .catch(error => console.error('Error:', error));
}
```

### 2. WebSocket实时数据

```javascript
// WebSocket连接
const ws = new WebSocket('ws://your-api-server/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    updateRealTimeData(data);
};

function updateRealTimeData(data) {
    // 更新页面数据
    Object.keys(data).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            element.textContent = data[key];
        }
    });
}
```

### 3. API服务集成

#### RESTful API示例
```javascript
class MiningAPI {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }

    async getDustData() {
        const response = await fetch(`${this.baseURL}/api/dust/current`);
        return response.json();
    }

    async getGasData() {
        const response = await fetch(`${this.baseURL}/api/gas/current`);
        return response.json();
    }

    async getPersonnelData() {
        const response = await fetch(`${this.baseURL}/api/personnel/locations`);
        return response.json();
    }

    async getEquipmentStatus() {
        const response = await fetch(`${this.baseURL}/api/equipment/status`);
        return response.json();
    }
}

// 使用示例
const api = new MiningAPI('http://your-api-server');
```

## 🔌 硬件集成

### 1. 传感器数据接入

#### MQTT协议
```javascript
// 使用MQTT.js库
const mqtt = require('mqtt');
const client = mqtt.connect('mqtt://your-mqtt-broker');

client.on('connect', function () {
    client.subscribe('sensors/dust/+');
    client.subscribe('sensors/gas/+');
    client.subscribe('sensors/water/+');
});

client.on('message', function (topic, message) {
    const data = JSON.parse(message.toString());
    updateSensorData(topic, data);
});
```

#### OPC UA协议
```javascript
// 使用node-opcua库
const opcua = require("node-opcua");

async function connectOPCUA() {
    const client = opcua.OPCUAClient.create({
        endpoint_must_exist: false,
    });
    
    await client.connect("opc.tcp://your-opcua-server:4840");
    const session = await client.createSession();
    
    // 读取数据
    const dataValue = await session.readVariableValue("ns=1;s=DustLevel");
    console.log("Dust Level:", dataValue.value.value);
}
```

### 2. 视频流集成

#### WebRTC视频流
```javascript
// 获取视频流
async function getVideoStream() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: { width: 1280, height: 720 },
            audio: false
        });
        
        const videoElement = document.getElementById('videoMonitor');
        videoElement.srcObject = stream;
    } catch (error) {
        console.error('Error accessing camera:', error);
    }
}
```

#### RTSP视频流
```html
<!-- 使用video.js播放RTSP流 -->
<video id="rtsp-player" class="video-js" controls preload="auto">
    <source src="rtsp://your-camera-ip/stream" type="application/x-rtsp">
</video>
```

## 🤖 AI模型集成

### 1. 模型推理API

```javascript
class AIModelService {
    constructor(apiBase) {
        this.apiBase = apiBase;
    }

    async predictDustLevel(imageData) {
        const response = await fetch(`${this.apiBase}/models/dust/predict`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ image: imageData })
        });
        return response.json();
    }

    async predictGasConcentration(sensorData) {
        const response = await fetch(`${this.apiBase}/models/gas/predict`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sensors: sensorData })
        });
        return response.json();
    }

    async detectAnomalies(equipmentData) {
        const response = await fetch(`${this.apiBase}/models/anomaly/detect`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ data: equipmentData })
        });
        return response.json();
    }
}
```

### 2. 边缘计算集成

```javascript
// 边缘节点状态监控
class EdgeNodeManager {
    constructor() {
        this.nodes = new Map();
    }

    registerNode(nodeId, endpoint) {
        this.nodes.set(nodeId, {
            id: nodeId,
            endpoint: endpoint,
            status: 'offline',
            lastHeartbeat: null
        });
    }

    async checkNodeHealth(nodeId) {
        const node = this.nodes.get(nodeId);
        if (!node) return false;

        try {
            const response = await fetch(`${node.endpoint}/health`);
            if (response.ok) {
                node.status = 'online';
                node.lastHeartbeat = new Date();
                return true;
            }
        } catch (error) {
            node.status = 'offline';
        }
        return false;
    }

    async deployModel(nodeId, modelConfig) {
        const node = this.nodes.get(nodeId);
        if (!node || node.status !== 'online') {
            throw new Error('Node not available');
        }

        const response = await fetch(`${node.endpoint}/models/deploy`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(modelConfig)
        });

        return response.json();
    }
}
```

## 🔒 安全配置

### 1. HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}
```

### 2. 访问控制
```nginx
# IP白名单
location / {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
}

# 基本认证
location /admin {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
}
```

## 📊 监控与日志

### 1. 系统监控
```javascript
// 性能监控
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoadTime: 0,
            apiResponseTime: 0,
            errorCount: 0
        };
    }

    trackPageLoad() {
        window.addEventListener('load', () => {
            this.metrics.pageLoadTime = performance.now();
            this.sendMetrics();
        });
    }

    trackAPICall(url, startTime) {
        const endTime = performance.now();
        this.metrics.apiResponseTime = endTime - startTime;
        this.sendMetrics();
    }

    sendMetrics() {
        fetch('/api/metrics', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(this.metrics)
        });
    }
}
```

### 2. 错误日志
```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
    const errorInfo = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };

    // 发送错误日志到服务器
    fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo)
    });
});
```

## 🔄 维护与更新

### 1. 自动化部署
```yaml
# GitHub Actions示例
name: Deploy Mining AI System

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Deploy to server
      run: |
        rsync -avz --delete ./ user@server:/var/www/mining-ai/
        
    - name: Restart services
      run: |
        ssh user@server 'sudo systemctl reload nginx'
```

### 2. 版本管理
```javascript
// 版本检查
class VersionManager {
    constructor() {
        this.currentVersion = '1.0.0';
    }

    async checkForUpdates() {
        try {
            const response = await fetch('/api/version');
            const data = await response.json();
            
            if (data.version !== this.currentVersion) {
                this.showUpdateNotification(data.version);
            }
        } catch (error) {
            console.error('Version check failed:', error);
        }
    }

    showUpdateNotification(newVersion) {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.innerHTML = `
            <p>发现新版本 ${newVersion}，是否立即更新？</p>
            <button onclick="location.reload()">立即更新</button>
            <button onclick="this.parentElement.remove()">稍后提醒</button>
        `;
        document.body.appendChild(notification);
    }
}
```

## 📞 技术支持

如需技术支持或有部署问题，请联系：
- 技术支持邮箱：<EMAIL>
- 文档地址：https://docs.mining-ai.com
- 问题反馈：https://github.com/your-repo/issues

---

**注意**：本部署指南提供了基础的部署方案，实际生产环境部署时需要根据具体的硬件配置、网络环境和安全要求进行相应调整。
