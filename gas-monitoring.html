<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瓦斯监测 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .gas-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .concentration-map {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .map-container {
            height: 300px;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .map-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .gas-sensor {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
        }

        .sensor-normal { background: var(--success-color); }
        .sensor-warning { background: var(--warning-color); }
        .sensor-danger { background: var(--danger-color); }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
        }

        .sensor-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
            display: none;
        }

        .map-legend {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: var(--spacing-sm);
            background: var(--bg-color);
            border-radius: var(--radius-sm);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 12px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .sensor-list {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            max-height: 400px;
            overflow-y: auto;
        }

        .sensor-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
        }

        .sensor-item:hover {
            background: var(--bg-color);
        }

        .sensor-item:last-child {
            border-bottom: none;
        }

        .sensor-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .sensor-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .sensor-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .sensor-location {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .sensor-reading {
            text-align: right;
        }

        .sensor-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        .sensor-unit {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .prediction-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .prediction-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .prediction-chart {
            height: 200px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .ventilation-control {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .fan-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .fan-control {
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            padding: var(--spacing-sm);
            text-align: center;
        }

        .fan-icon {
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
            animation: rotate 3s linear infinite;
        }

        .fan-icon.stopped {
            animation: none;
            color: var(--text-muted);
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .fan-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .fan-status {
            font-size: 12px;
            margin-bottom: var(--spacing-xs);
        }

        .fan-controls {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .fan-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .fan-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .alert-system {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-top: var(--spacing-md);
        }

        .alert-levels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .alert-level {
            text-align: center;
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            border: 2px solid transparent;
        }

        .alert-level.active {
            border-color: var(--primary-color);
        }

        .alert-level-1 { background: rgba(16, 185, 129, 0.1); }
        .alert-level-2 { background: rgba(245, 158, 11, 0.1); }
        .alert-level-3 { background: rgba(239, 68, 68, 0.1); }

        .alert-level-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .alert-level-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .alert-history {
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('gas-monitoring.html', '瓦斯监测', ['首页', '瓦斯监测']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 瓦斯监测仪表板 -->
            <div class="gas-dashboard">
                <!-- 浓度分布图 -->
                <div class="concentration-map">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-map-marked-alt"></i>
                        瓦斯浓度分布图
                    </h3>
                    <div class="map-container">
                        <div class="map-overlay"></div>
                        <!-- 传感器点位 -->
                        <div class="gas-sensor sensor-normal" style="top: 20%; left: 15%;" data-sensor="GS001" data-value="0.12" data-location="主巷道入口"></div>
                        <div class="gas-sensor sensor-normal" style="top: 30%; left: 40%;" data-sensor="GS002" data-value="0.08" data-location="1号工作面"></div>
                        <div class="gas-sensor sensor-warning" style="top: 50%; left: 60%;" data-sensor="GS003" data-value="0.35" data-location="2号工作面"></div>
                        <div class="gas-sensor sensor-normal" style="top: 70%; left: 25%;" data-sensor="GS004" data-value="0.15" data-location="回风巷道"></div>
                        <div class="gas-sensor sensor-danger" style="top: 40%; left: 80%;" data-sensor="GS005" data-value="0.68" data-location="3号工作面"></div>
                        <div class="gas-sensor sensor-normal" style="top: 60%; left: 45%;" data-sensor="GS006" data-value="0.09" data-location="运输巷道"></div>
                        
                        <div class="sensor-tooltip" id="sensorTooltip"></div>
                    </div>
                    <div class="map-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: var(--success-color);"></div>
                            <span>正常 (≤0.3%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: var(--warning-color);"></div>
                            <span>警告 (0.3-0.5%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: var(--danger-color);"></div>
                            <span>危险 (>0.5%)</span>
                        </div>
                    </div>
                </div>

                <!-- 传感器列表 -->
                <div class="sensor-list">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-list"></i>
                        传感器实时数据
                    </h3>
                    <div class="sensor-item">
                        <div class="sensor-info">
                            <div class="sensor-status" style="background: var(--success-color);"></div>
                            <div>
                                <div class="sensor-name">GS001</div>
                                <div class="sensor-location">主巷道入口</div>
                            </div>
                        </div>
                        <div class="sensor-reading">
                            <div class="sensor-value" id="gs001">0.12 <span class="sensor-unit">%</span></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-info">
                            <div class="sensor-status" style="background: var(--success-color);"></div>
                            <div>
                                <div class="sensor-name">GS002</div>
                                <div class="sensor-location">1号工作面</div>
                            </div>
                        </div>
                        <div class="sensor-reading">
                            <div class="sensor-value" id="gs002">0.08 <span class="sensor-unit">%</span></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-info">
                            <div class="sensor-status" style="background: var(--warning-color);"></div>
                            <div>
                                <div class="sensor-name">GS003</div>
                                <div class="sensor-location">2号工作面</div>
                            </div>
                        </div>
                        <div class="sensor-reading">
                            <div class="sensor-value" id="gs003">0.35 <span class="sensor-unit">%</span></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-info">
                            <div class="sensor-status" style="background: var(--success-color);"></div>
                            <div>
                                <div class="sensor-name">GS004</div>
                                <div class="sensor-location">回风巷道</div>
                            </div>
                        </div>
                        <div class="sensor-reading">
                            <div class="sensor-value" id="gs004">0.15 <span class="sensor-unit">%</span></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-info">
                            <div class="sensor-status" style="background: var(--danger-color);"></div>
                            <div>
                                <div class="sensor-name">GS005</div>
                                <div class="sensor-location">3号工作面</div>
                            </div>
                        </div>
                        <div class="sensor-reading">
                            <div class="sensor-value" id="gs005">0.68 <span class="sensor-unit">%</span></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-info">
                            <div class="sensor-status" style="background: var(--success-color);"></div>
                            <div>
                                <div class="sensor-name">GS006</div>
                                <div class="sensor-location">运输巷道</div>
                            </div>
                        </div>
                        <div class="sensor-reading">
                            <div class="sensor-value" id="gs006">0.09 <span class="sensor-unit">%</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI预测分析 -->
            <div class="prediction-panel">
                <div class="prediction-card">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        浓度趋势预测
                    </h3>
                    <div class="prediction-chart">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area" style="font-size: 48px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                    <div style="font-size: 14px; color: var(--text-secondary);">
                        <p><strong>LSTM模型预测：</strong></p>
                        <p>• 未来2小时内，3号工作面瓦斯浓度将持续上升</p>
                        <p>• 预计峰值：0.85%（1小时20分钟后）</p>
                        <p>• 建议立即加强通风</p>
                    </div>
                </div>

                <div class="prediction-card">
                    <h3 class="card-title">
                        <i class="fas fa-wind"></i>
                        通风网络分析
                    </h3>
                    <div class="prediction-chart">
                        <div class="chart-placeholder">
                            <i class="fas fa-project-diagram" style="font-size: 48px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                    <div style="font-size: 14px; color: var(--text-secondary);">
                        <p><strong>网络状态分析：</strong></p>
                        <p>• 主风机运行正常，风量：1200m³/min</p>
                        <p>• 局部通风机3台运行，1台故障</p>
                        <p>• 风压分布均匀，无明显阻塞</p>
                    </div>
                </div>

                <div class="prediction-card">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        异常波动检测
                    </h3>
                    <div class="prediction-chart">
                        <div class="chart-placeholder">
                            <i class="fas fa-wave-square" style="font-size: 48px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                    <div style="font-size: 14px; color: var(--text-secondary);">
                        <p><strong>异常检测结果：</strong></p>
                        <p>• 检测到3号工作面异常波动</p>
                        <p>• 波动幅度：±0.15%</p>
                        <p>• 可能原因：通风不畅或瓦斯涌出</p>
                    </div>
                </div>
            </div>

            <!-- 通风控制系统 -->
            <div class="ventilation-control">
                <h3 class="card-title mb-3">
                    <i class="fas fa-fan"></i>
                    智能通风控制系统
                </h3>
                <div class="fan-grid">
                    <div class="fan-control">
                        <div class="fan-icon">
                            <i class="fas fa-fan"></i>
                        </div>
                        <div class="fan-name">主风机1号</div>
                        <div class="fan-status" style="color: var(--success-color);">运行中</div>
                        <div class="fan-controls">
                            <button class="fan-btn">启动</button>
                            <button class="fan-btn">停止</button>
                            <button class="fan-btn">调速</button>
                        </div>
                    </div>
                    <div class="fan-control">
                        <div class="fan-icon">
                            <i class="fas fa-fan"></i>
                        </div>
                        <div class="fan-name">主风机2号</div>
                        <div class="fan-status" style="color: var(--success-color);">运行中</div>
                        <div class="fan-controls">
                            <button class="fan-btn">启动</button>
                            <button class="fan-btn">停止</button>
                            <button class="fan-btn">调速</button>
                        </div>
                    </div>
                    <div class="fan-control">
                        <div class="fan-icon stopped">
                            <i class="fas fa-fan"></i>
                        </div>
                        <div class="fan-name">局扇3号</div>
                        <div class="fan-status" style="color: var(--danger-color);">故障</div>
                        <div class="fan-controls">
                            <button class="fan-btn">启动</button>
                            <button class="fan-btn">停止</button>
                            <button class="fan-btn">维修</button>
                        </div>
                    </div>
                    <div class="fan-control">
                        <div class="fan-icon">
                            <i class="fas fa-fan"></i>
                        </div>
                        <div class="fan-name">局扇4号</div>
                        <div class="fan-status" style="color: var(--success-color);">运行中</div>
                        <div class="fan-controls">
                            <button class="fan-btn">启动</button>
                            <button class="fan-btn">停止</button>
                            <button class="fan-btn">调速</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能预警系统 -->
            <div class="alert-system">
                <h3 class="card-title mb-3">
                    <i class="fas fa-shield-alt"></i>
                    智能预警系统
                </h3>
                <div class="alert-levels">
                    <div class="alert-level alert-level-1 active">
                        <div class="alert-level-title" style="color: var(--success-color);">一级预警</div>
                        <div class="alert-level-desc">浓度正常<br>≤ 0.3%</div>
                    </div>
                    <div class="alert-level alert-level-2">
                        <div class="alert-level-title" style="color: var(--warning-color);">二级预警</div>
                        <div class="alert-level-desc">浓度偏高<br>0.3% - 0.5%</div>
                    </div>
                    <div class="alert-level alert-level-3">
                        <div class="alert-level-title" style="color: var(--danger-color);">三级预警</div>
                        <div class="alert-level-desc">浓度危险<br>> 0.5%</div>
                    </div>
                </div>
                <div class="alert-history">
                    <h4 style="margin-bottom: var(--spacing-sm);">预警历史记录</h4>
                    <div class="alert-item alert-danger">
                        <i class="fas fa-times-circle"></i>
                        <div>
                            <strong>三级预警</strong> - 3号工作面瓦斯浓度达到0.68%，超过危险阈值
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                刚刚 | 已启动应急通风
                            </div>
                        </div>
                    </div>
                    <div class="alert-item alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <strong>二级预警</strong> - 2号工作面瓦斯浓度达到0.35%，需要关注
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                5分钟前 | 已加强监测
                            </div>
                        </div>
                    </div>
                    <div class="alert-item alert-normal">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <strong>预警解除</strong> - 1号工作面瓦斯浓度恢复正常
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                30分钟前 | 系统自动解除
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 传感器悬停提示
        const sensors = document.querySelectorAll('.gas-sensor');
        const tooltip = document.getElementById('sensorTooltip');

        sensors.forEach(sensor => {
            sensor.addEventListener('mouseenter', (e) => {
                const sensorId = e.target.dataset.sensor;
                const value = e.target.dataset.value;
                const location = e.target.dataset.location;
                
                tooltip.innerHTML = `
                    <strong>${sensorId}</strong><br>
                    ${location}<br>
                    瓦斯浓度: ${value}%
                `;
                tooltip.style.display = 'block';
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            });

            sensor.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('gs001', 0.05, 0.20, '%', 2);
        MiningAI.simulateRealTimeData('gs002', 0.03, 0.15, '%', 2);
        MiningAI.simulateRealTimeData('gs003', 0.25, 0.45, '%', 2);
        MiningAI.simulateRealTimeData('gs004', 0.08, 0.25, '%', 2);
        MiningAI.simulateRealTimeData('gs005', 0.50, 0.80, '%', 2);
        MiningAI.simulateRealTimeData('gs006', 0.05, 0.18, '%', 2);

        // 风机控制按钮事件
        document.querySelectorAll('.fan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.textContent;
                const fanName = e.target.closest('.fan-control').querySelector('.fan-name').textContent;
                MiningAI.showNotification(`${fanName} ${action}操作已执行`, 'info');
            });
        });
    </script>
</body>
</html>
