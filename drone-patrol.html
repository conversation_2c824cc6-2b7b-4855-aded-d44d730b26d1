<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无人机巡航 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .drone-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .flight-control {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .flight-map {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .map-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .control-group {
            display: flex;
            gap: var(--spacing-xs);
        }

        .control-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: var(--bg-color);
        }

        .control-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .flight-canvas {
            height: 400px;
            background: linear-gradient(135deg, #0f172a, #1e293b);
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
            border: 2px solid var(--border-color);
        }

        .terrain-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="terrain" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23terrain)"/></svg>');
        }

        .flight-path {
            position: absolute;
            border: 2px dashed #00ff88;
            border-radius: 2px;
            animation: pathPulse 3s ease-in-out infinite;
        }

        @keyframes pathPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .waypoint {
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 8px;
            font-weight: 600;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: var(--shadow-md);
        }

        .waypoint-planned {
            background: var(--info-color);
        }

        .waypoint-completed {
            background: var(--success-color);
        }

        .waypoint-current {
            background: var(--warning-color);
            animation: pulse 2s infinite;
        }

        .waypoint-failed {
            background: var(--danger-color);
        }

        .drone-marker {
            position: absolute;
            width: 32px;
            height: 32px;
            color: #00ff88;
            font-size: 24px;
            cursor: pointer;
            animation: droneHover 3s ease-in-out infinite;
            filter: drop-shadow(0 0 8px #00ff88);
        }

        @keyframes droneHover {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .risk-area {
            position: absolute;
            border: 2px solid var(--danger-color);
            border-radius: var(--radius-sm);
            background: rgba(239, 68, 68, 0.2);
            animation: riskBlink 2s ease-in-out infinite;
        }

        @keyframes riskBlink {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        .drone-status {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .status-item {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .status-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .status-label {
            font-size: 10px;
            color: var(--text-secondary);
        }

        .drone-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }

        .drone-btn {
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .drone-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .drone-btn.danger:hover {
            background: var(--danger-color);
            color: white;
        }

        .ai-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .analysis-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .analysis-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .analysis-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .video-feed {
            height: 200px;
            background: #000;
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .video-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        .video-overlay {
            position: absolute;
            top: var(--spacing-xs);
            left: var(--spacing-xs);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 10px;
        }

        .detection-results {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .risk-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .risk-item:last-child {
            margin-bottom: 0;
        }

        .risk-critical {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }

        .risk-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning-color);
        }

        .risk-info {
            background: rgba(6, 182, 212, 0.1);
            border-left: 4px solid var(--info-color);
        }

        .mission-management {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .mission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .mission-item:last-child {
            border-bottom: none;
        }

        .mission-info {
            flex: 1;
        }

        .mission-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 13px;
        }

        .mission-details {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .mission-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .status-scheduled {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info-color);
        }

        .status-flying {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-completed {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-failed {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .flight-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
            display: none;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('drone-patrol.html', '无人机巡航', ['首页', '无人机巡航']);

        // 页面内容
        pageContent.innerHTML = `
            <!-- 无人机概览 -->
            <div class="drone-overview">
                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-helicopter"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">在线无人机</div>
                                <div class="data-card-value" id="activeDrones">3 <span class="data-card-unit">架</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">今日任务</div>
                                <div class="data-card-value" id="todayMissions">8 <span class="data-card-unit">次</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">发现风险</div>
                                <div class="data-card-value" id="risksDetected">5 <span class="data-card-unit">处</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">飞行时长</div>
                                <div class="data-card-value" id="flightTime">6.5 <span class="data-card-unit">小时</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 飞行控制 -->
            <div class="flight-control">
                <div class="flight-map">
                    <div class="map-controls">
                        <div class="control-group">
                            <button class="control-btn active">实时视图</button>
                            <button class="control-btn">航线规划</button>
                            <button class="control-btn">历史轨迹</button>
                        </div>
                        <div class="control-group">
                            <button class="control-btn" title="放大">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="control-btn" title="缩小">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button class="control-btn" title="重置视图">
                                <i class="fas fa-home"></i>
                            </button>
                            <button class="control-btn" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flight-canvas" id="flightCanvas">
                        <div class="terrain-overlay"></div>

                        <!-- 飞行路径 -->
                        <div class="flight-path" style="top: 20%; left: 10%; width: 25%; height: 0; border-top: 2px dashed #00ff88;"></div>
                        <div class="flight-path" style="top: 20%; left: 35%; width: 0; height: 30%; border-left: 2px dashed #00ff88;"></div>
                        <div class="flight-path" style="top: 50%; left: 35%; width: 30%; height: 0; border-top: 2px dashed #00ff88;"></div>
                        <div class="flight-path" style="top: 50%; left: 65%; width: 0; height: 25%; border-left: 2px dashed #00ff88;"></div>
                        <div class="flight-path" style="top: 75%; left: 65%; width: 25%; height: 0; border-top: 2px dashed #00ff88;"></div>

                        <!-- 航点 -->
                        <div class="waypoint waypoint-completed" style="top: 15%; left: 8%;"
                             data-waypoint="1" data-name="起飞点" data-status="已完成" data-time="08:00">1</div>
                        <div class="waypoint waypoint-completed" style="top: 15%; left: 33%;"
                             data-waypoint="2" data-name="北区巡检点" data-status="已完成" data-time="08:15">2</div>
                        <div class="waypoint waypoint-current" style="top: 45%; left: 33%;"
                             data-waypoint="3" data-name="中央监测点" data-status="巡检中" data-time="08:30">3</div>
                        <div class="waypoint waypoint-planned" style="top: 45%; left: 63%;"
                             data-waypoint="4" data-name="东区检查点" data-status="计划中" data-time="">4</div>
                        <div class="waypoint waypoint-planned" style="top: 70%; left: 63%;"
                             data-waypoint="5" data-name="南区监测点" data-status="计划中" data-time="">5</div>
                        <div class="waypoint waypoint-planned" style="top: 70%; left: 88%;"
                             data-waypoint="6" data-name="返航点" data-status="计划中" data-time="">6</div>

                        <!-- 无人机位置 -->
                        <div class="drone-marker" style="top: 40%; left: 38%;"
                             data-drone="UAV-001" data-battery="78%" data-altitude="50m" data-speed="15km/h">
                            <i class="fas fa-helicopter"></i>
                        </div>

                        <!-- 风险区域 -->
                        <div class="risk-area" style="top: 60%; left: 45%; width: 15%; height: 20%;"
                             data-risk="边坡裂缝" data-severity="高"></div>
                        <div class="risk-area" style="top: 25%; left: 70%; width: 12%; height: 15%;"
                             data-risk="积水区域" data-severity="中"></div>

                        <div class="flight-tooltip" id="flightTooltip"></div>
                    </div>
                </div>

                <!-- 无人机状态 -->
                <div class="drone-status">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-helicopter"></i>
                        UAV-001 状态
                    </h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-value" id="batteryLevel">78%</div>
                            <div class="status-label">电池电量</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="altitude">50m</div>
                            <div class="status-label">飞行高度</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="speed">15km/h</div>
                            <div class="status-label">飞行速度</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="signal">-65dBm</div>
                            <div class="status-label">信号强度</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="temperature">25°C</div>
                            <div class="status-label">机体温度</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="flightMode">自动</div>
                            <div class="status-label">飞行模式</div>
                        </div>
                    </div>

                    <div class="drone-controls">
                        <button class="drone-btn">
                            <i class="fas fa-play"></i>
                            开始任务
                        </button>
                        <button class="drone-btn">
                            <i class="fas fa-pause"></i>
                            暂停任务
                        </button>
                        <button class="drone-btn">
                            <i class="fas fa-home"></i>
                            返航
                        </button>
                        <button class="drone-btn danger">
                            <i class="fas fa-stop"></i>
                            紧急停止
                        </button>
                    </div>
                </div>
            </div>

            <!-- AI图像识别分析 -->
            <div class="ai-analysis">
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="analysis-title">实时图像识别</div>
                    </div>
                    <div class="video-feed">
                        <div class="video-placeholder">
                            <i class="fas fa-video" style="font-size: 32px;"></i>
                        </div>
                        <div class="video-overlay">
                            <i class="fas fa-circle" style="color: #ef4444; animation: blink 1s infinite;"></i>
                            实时传输
                        </div>
                    </div>
                    <div class="detection-results">
                        <p><strong>AI检测结果:</strong></p>
                        <p>• 边坡稳定性: 正常</p>
                        <p>• 植被覆盖: 良好</p>
                        <p>• 排水状况: 正常</p>
                        <p>• 异物检测: 未发现</p>
                        <p>• 裂缝识别: 发现1处微小裂缝</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="analysis-title">风险识别预警</div>
                    </div>
                    <div class="risk-item risk-critical">
                        <i class="fas fa-mountain"></i>
                        <div>
                            <strong>边坡裂缝风险</strong><br>
                            <small>东南区域发现长度约2米的裂缝，建议立即检查</small>
                        </div>
                    </div>
                    <div class="risk-item risk-warning">
                        <i class="fas fa-tint"></i>
                        <div>
                            <strong>积水区域</strong><br>
                            <small>北区检测到积水，可能影响设备运行</small>
                        </div>
                    </div>
                    <div class="risk-item risk-info">
                        <i class="fas fa-leaf"></i>
                        <div>
                            <strong>植被异常</strong><br>
                            <small>西区植被覆盖率下降，需要关注</small>
                        </div>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="analysis-title">趋势分析</div>
                    </div>
                    <div class="detection-results">
                        <p><strong>历史数据对比:</strong></p>
                        <p>• 边坡稳定性较上月提升5%</p>
                        <p>• 积水问题较上周增加2处</p>
                        <p>• 植被覆盖率保持稳定</p>
                        <p>• 新增风险点1个</p>
                        <br>
                        <p><strong>预测建议:</strong></p>
                        <p>• 建议加强东南区域监测</p>
                        <p>• 雨季前需清理排水系统</p>
                        <p>• 定期检查边坡稳定性</p>
                    </div>
                </div>
            </div>

            <!-- 任务管理 -->
            <div class="mission-management">
                <h3 class="card-title mb-3">
                    <i class="fas fa-tasks"></i>
                    巡航任务管理
                </h3>
                <div class="mission-item">
                    <div class="mission-info">
                        <div class="mission-title">定时安全巡检</div>
                        <div class="mission-details">UAV-001 | 每日08:00 | 全区域巡检 | 预计2小时</div>
                    </div>
                    <div class="mission-status status-flying">执行中</div>
                </div>
                <div class="mission-item">
                    <div class="mission-info">
                        <div class="mission-title">边坡稳定性监测</div>
                        <div class="mission-details">UAV-002 | 每周一、三、五 | 重点区域 | 预计1小时</div>
                    </div>
                    <div class="mission-status status-scheduled">已计划</div>
                </div>
                <div class="mission-item">
                    <div class="mission-info">
                        <div class="mission-title">应急响应巡航</div>
                        <div class="mission-details">UAV-003 | 报警触发 | 指定区域 | 预计30分钟</div>
                    </div>
                    <div class="mission-status status-completed">已完成</div>
                </div>
                <div class="mission-item">
                    <div class="mission-info">
                        <div class="mission-title">夜间安全巡逻</div>
                        <div class="mission-details">UAV-001 | 每日20:00 | 关键设施 | 预计1.5小时</div>
                    </div>
                    <div class="mission-status status-scheduled">已计划</div>
                </div>
                <div class="mission-item">
                    <div class="mission-info">
                        <div class="mission-title">环境监测任务</div>
                        <div class="mission-details">UAV-002 | 昨日14:00 | 排放监测 | 计划1小时</div>
                    </div>
                    <div class="mission-status status-failed">任务失败</div>
                </div>
            </div>
        `;

        // 地图控制按钮事件
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除其他按钮的active状态（仅对同组按钮）
                const group = e.target.closest('.control-group');
                if (group && !e.target.title) { // 只对没有title的按钮（非工具按钮）进行切换
                    group.querySelectorAll('.control-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                }

                const action = e.target.textContent.trim() || e.target.title;
                MiningAI.showNotification(`${action}功能已激活`, 'info');
            });
        });

        // 无人机控制按钮事件
        document.querySelectorAll('.drone-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.textContent.trim();
                const isDanger = e.target.classList.contains('danger');

                if (isDanger) {
                    MiningAI.showNotification(`${action}已执行`, 'warning');
                } else {
                    MiningAI.showNotification(`${action}已执行`, 'info');
                }
            });
        });

        // 航点和风险区域悬停提示
        const flightElements = document.querySelectorAll('.waypoint, .drone-marker, .risk-area');
        const tooltip = document.getElementById('flightTooltip');

        flightElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                let tooltipContent = '';

                if (element.classList.contains('waypoint')) {
                    const waypoint = e.target.dataset.waypoint;
                    const name = e.target.dataset.name;
                    const status = e.target.dataset.status;
                    const time = e.target.dataset.time;

                    tooltipContent = `
                        <strong>航点 ${waypoint}</strong><br>
                        ${name}<br>
                        状态: ${status}<br>
                        ${time ? `时间: ${time}` : ''}
                    `;
                } else if (element.classList.contains('drone-marker')) {
                    const drone = e.target.dataset.drone;
                    const battery = e.target.dataset.battery;
                    const altitude = e.target.dataset.altitude;
                    const speed = e.target.dataset.speed;

                    tooltipContent = `
                        <strong>${drone}</strong><br>
                        电池: ${battery}<br>
                        高度: ${altitude}<br>
                        速度: ${speed}
                    `;
                } else if (element.classList.contains('risk-area')) {
                    const risk = e.target.dataset.risk;
                    const severity = e.target.dataset.severity;

                    tooltipContent = `
                        <strong>风险区域</strong><br>
                        类型: ${risk}<br>
                        严重程度: ${severity}
                    `;
                }

                tooltip.innerHTML = tooltipContent;
                tooltip.style.display = 'block';
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            });

            element.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });

            element.addEventListener('click', (e) => {
                if (element.classList.contains('waypoint')) {
                    const waypoint = e.target.dataset.waypoint;
                    const name = e.target.dataset.name;
                    MiningAI.showNotification(`已选择航点${waypoint}: ${name}`, 'info');
                } else if (element.classList.contains('drone-marker')) {
                    const drone = e.target.dataset.drone;
                    MiningAI.showNotification(`已选择无人机: ${drone}`, 'info');
                } else if (element.classList.contains('risk-area')) {
                    const risk = e.target.dataset.risk;
                    MiningAI.showNotification(`已选择风险区域: ${risk}`, 'warning');
                }
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('activeDrones', 2, 4, '架', 0);
        MiningAI.simulateRealTimeData('todayMissions', 6, 12, '次', 0);
        MiningAI.simulateRealTimeData('risksDetected', 3, 8, '处', 0);
        MiningAI.simulateRealTimeData('flightTime', 5.0, 8.0, '小时', 1);

        // 无人机状态实时更新
        MiningAI.simulateRealTimeData('batteryLevel', 70, 85, '%', 0);
        MiningAI.simulateRealTimeData('altitude', 45, 60, 'm', 0);
        MiningAI.simulateRealTimeData('speed', 10, 20, 'km/h', 0);
        MiningAI.simulateRealTimeData('temperature', 20, 30, '°C', 0);

        // 模拟无人机移动
        setInterval(() => {
            const droneMarker = document.querySelector('.drone-marker');
            if (droneMarker) {
                const currentTop = parseFloat(droneMarker.style.top);
                const currentLeft = parseFloat(droneMarker.style.left);

                // 沿着航线移动
                const newTop = currentTop + (Math.random() - 0.5) * 2;
                const newLeft = currentLeft + (Math.random() - 0.5) * 2;

                // 确保不超出边界
                if (newTop > 10 && newTop < 80) {
                    droneMarker.style.top = newTop + '%';
                }
                if (newLeft > 10 && newLeft < 85) {
                    droneMarker.style.left = newLeft + '%';
                }
            }
        }, 5000);

        // 模拟航点状态更新
        setInterval(() => {
            const waypoints = document.querySelectorAll('.waypoint');
            waypoints.forEach(waypoint => {
                if (waypoint.classList.contains('waypoint-current')) {
                    // 当前航点有概率完成
                    if (Math.random() < 0.3) {
                        waypoint.classList.remove('waypoint-current');
                        waypoint.classList.add('waypoint-completed');

                        // 激活下一个航点
                        const nextWaypoint = waypoint.nextElementSibling;
                        if (nextWaypoint && nextWaypoint.classList.contains('waypoint-planned')) {
                            nextWaypoint.classList.remove('waypoint-planned');
                            nextWaypoint.classList.add('waypoint-current');
                        }
                    }
                }
            });
        }, 15000);

        // 添加闪烁动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0.3; }
            }
        `;
        document.head.appendChild(style);

        // 显示系统启动消息
        setTimeout(() => {
            MiningAI.showNotification('无人机巡航系统已加载', 'success');
        }, 1000);
    </script>
</body>
</html>
