# 矿业AI智能监控平台

一个基于AI技术的矿业安全生产智能监控管理系统，集成了粉尘监控、瓦斯检测、排水控制、设备监控、人员安全、数据中台和运维管理等核心功能模块。

## 🚀 项目特色

- **智能化监控**：基于AI深度学习的实时监控和预警系统
- **边云协同**：边缘计算与云端服务相结合的混合架构
- **模块化设计**：7大核心功能模块，覆盖矿业生产全流程
- **实时数据**：毫秒级数据处理和实时状态更新
- **预测性维护**：基于机器学习的设备故障预测
- **响应式设计**：支持PC端和移动端访问

## 📋 功能模块

### 1. 系统首页
- 各系统入口导航
- 关键指标统计展示
- 实时数据概览
- 快速操作面板

### 2. 粉尘视觉识别与智能抑尘
- 高清视频采集与图像处理
- YOLO + U-Net 粉尘识别算法
- 激光粉尘传感器数据融合
- 智能喷淋控制系统联动
- 实时报警与远程推送

### 3. 瓦斯监测与智能预警
- 多点瓦斯、甲烷、一氧化碳监测
- LSTM时序预测模型
- 风速风量通风网络分析
- AI预测浓度趋势
- 风机联动控制系统

### 4. 智能排水控制与预测维护
- 水位、水泵运行状态监测
- LSTM水位变化预测
- 智能水泵启停/轮换控制
- 故障诊断与预警
- 能效分析与优化

### 5. 设备状态监控与预测性维护
- 通风机、皮带机、破碎机等设备监控
- 振动、温度、电流等多参数采集
- 孤立森林异常检测算法
- 故障模式识别与预警
- 自动生成维护建议

### 6. 人员行为分析与定位安全
- UWB/Zigbee/WiFi定位系统
- 视频行为分析（安全帽检测、跌倒识别）
- 可穿戴设备健康监测
- 区域滞留与异常行为报警
- 人员活动轨迹可视化

### 7. AI数据中台与智能分析
- 异构数据统一采集（MQTT/OPC UA/Modbus）
- Kafka + Flink + InfluxDB + MinIO架构
- 15个AI模型统一管理
- 模型训练与推理服务
- 智能分析与洞察

### 8. 边云协同平台部署与运维
- 边缘计算节点管理
- 云端服务集群监控
- OTA模型更新支持
- 系统高可用性保障
- 运维自动化与容灾恢复

### 9. 空间管理 ⭐（新增）
- 矿区所有作业空间与设备布置管理
- 支持2D/3D空间视图展示
- 分区管理与权限划分
- 与人员定位系统联动，实现区域超员预警
- 作业审批联控与访问权限管理

### 10. 巡检管理 ⭐（新增）
- 日常安全/设备巡检任务与路线管理
- 支持手持终端打卡/扫码/拍照取证
- AI分析巡检数据趋势，识别潜在隐患
- 巡检漏项提醒与闭环整改跟踪
- 移动端巡检助手与智能检查清单

### 11. 无人机智能巡航 ⭐（新增）
- 无人机定时/自主巡航任务管理
- 航线规划、自动起降、图像回传
- AI图像识别边坡裂缝、积水、异物等风险
- 联动安防预警与事件处理流程
- 实时飞行状态监控与远程控制

## 🛠️ 技术架构

### 前端技术栈
- **HTML5 + CSS3 + JavaScript**：原生Web技术
- **Font Awesome**：图标库
- **响应式设计**：支持多设备访问
- **模块化组件**：统一的导航和UI组件

### 数据处理架构
- **数据接入**：MQTT/OPC UA/Modbus协议
- **流处理**：Apache Kafka + Apache Flink
- **时序数据库**：InfluxDB
- **对象存储**：MinIO
- **缓存**：Redis

### AI模型技术
- **计算机视觉**：YOLO、U-Net、SegFormer
- **时序预测**：LSTM、Autoencoder
- **异常检测**：孤立森林、SVM
- **模型部署**：ONNX、Triton Inference Server

## 📁 项目结构

```
矿业AI管理系统/
├── index.html                 # 系统首页
├── dust-control.html          # 粉尘监控模块
├── gas-monitoring.html        # 瓦斯监测模块
├── drainage-control.html      # 排水控制模块
├── equipment-monitoring.html  # 设备监控模块
├── personnel-safety.html      # 人员安全模块
├── data-platform.html         # 数据中台模块
├── operations.html            # 运维管理模块
├── space-management.html      # 空间管理模块 ⭐新增
├── inspection-management.html # 巡检管理模块 ⭐新增
├── drone-patrol.html          # 无人机巡航模块 ⭐新增
├── assets/
│   ├── css/
│   │   └── common.css         # 公共样式文件
│   └── js/
│       └── common.js          # 公共JavaScript组件
└── README.md                  # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- HTTP服务器（用于本地开发）

### 安装运行

1. **克隆项目**
```bash
git clone [项目地址]
cd 矿业AI管理系统
```

2. **启动本地服务器**
```bash
# 使用Python
python3 -m http.server 8000

# 或使用Node.js
npx http-server -p 8000

# 或使用PHP
php -S localhost:8000
```

3. **访问系统**
打开浏览器访问：http://localhost:8000/index.html

## 🎯 核心特性

### 实时数据监控
- 2,450条/秒数据处理能力
- 15ms平均响应延迟
- 99.8%系统可用性
- 实时数据流可视化

### AI智能分析
- 15个AI模型并行运行
- 94.2%平均预测准确率
- 毫秒级推理响应
- 自动异常检测与预警

### 边云协同架构
- 12个边缘计算节点
- 8个云端服务集群
- 自动故障转移
- 负载均衡与弹性扩缩

### 安全生产保障
- 127人实时定位跟踪
- 多维度安全监测
- 智能行为分析
- 应急响应机制

### 空间与巡检管理 ⭐新增
- 12个管理区域实时监控
- 24项日常巡检任务
- 87.5%巡检完成率
- 智能路线规划与优化

### 无人机巡航系统 ⭐新增
- 3架无人机在线巡航
- 6.5小时日均飞行时长
- 5处风险自动识别
- AI图像识别准确率90%+

## 📊 系统指标

| 指标类型 | 数值 | 说明 |
|---------|------|------|
| 数据处理量 | 1.2TB | 日均数据处理量 |
| 实时数据流 | 2,450条/秒 | 传感器数据接入速度 |
| AI模型数量 | 15个 | 部署的AI模型总数 |
| 预测准确率 | 94.2% | AI模型平均准确率 |
| 系统可用性 | 99.8% | 系统运行可用性 |
| 响应延迟 | 15ms | 平均系统响应时间 |

## 🔧 配置说明

### 导航配置
在 `assets/js/common.js` 中修改 `navigationConfig` 数组来自定义导航菜单。

### 样式定制
在 `assets/css/common.css` 中的 `:root` 变量部分修改主题色彩和样式。

### 数据模拟
各页面的实时数据通过 `MiningAI.simulateRealTimeData()` 函数模拟，可根据实际需求替换为真实数据接口。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：[您的姓名]
- 邮箱：[您的邮箱]
- 项目链接：[项目地址]

---

**注意**：本项目为演示版本，实际部署时需要根据具体的硬件环境和业务需求进行相应的配置和集成。
