<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .data-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .data-source-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .source-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .source-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .source-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .source-status {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-online { background: var(--success-color); }
        .status-offline { background: var(--danger-color); }
        .status-warning { background: var(--warning-color); }

        .source-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }

        .metric-item {
            text-align: center;
            padding: var(--spacing-xs);
            background: var(--bg-color);
            border-radius: var(--radius-sm);
        }

        .metric-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .metric-label {
            font-size: 10px;
            color: var(--text-secondary);
        }

        .model-management {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-md);
        }

        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-sm);
        }

        .model-card {
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
        }

        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xs);
        }

        .model-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .model-version {
            font-size: 12px;
            color: var(--text-secondary);
            background: var(--card-bg);
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .model-info {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .model-metrics {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }

        .model-accuracy {
            color: var(--success-color);
            font-weight: 600;
        }

        .model-latency {
            color: var(--text-secondary);
        }

        .analytics-dashboard {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .chart-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .chart-container {
            height: 300px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .insight-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .insight-item {
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
            border-left: 4px solid var(--primary-color);
            background: rgba(59, 130, 246, 0.05);
        }

        .insight-item:last-child {
            margin-bottom: 0;
        }

        .insight-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 14px;
            margin-bottom: 4px;
        }

        .insight-content {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .system-architecture {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .architecture-diagram {
            height: 250px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .arch-component {
            position: absolute;
            background: var(--card-bg);
            border: 2px solid var(--primary-color);
            border-radius: var(--radius-sm);
            padding: var(--spacing-xs);
            text-align: center;
            font-size: 11px;
            font-weight: 500;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .arch-component:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.05);
        }

        .arch-arrow {
            position: absolute;
            color: var(--text-secondary);
            font-size: 16px;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .perf-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .perf-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin: 0 auto var(--spacing-sm);
        }

        .perf-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .perf-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .perf-trend {
            font-size: 11px;
            margin-top: 4px;
        }

        .trend-up {
            color: var(--success-color);
        }

        .trend-down {
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('data-platform.html', '数据中台', ['首页', '数据中台']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 数据概览 -->
            <div class="data-overview">
                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">总数据量</div>
                                <div class="data-card-value" id="totalData">1.2 <span class="data-card-unit">TB</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-stream"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">实时数据流</div>
                                <div class="data-card-value" id="dataStream">2,450 <span class="data-card-unit">条/秒</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">AI模型数量</div>
                                <div class="data-card-value" id="modelCount">15 <span class="data-card-unit">个</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">预测准确率</div>
                                <div class="data-card-value" id="accuracy">94.2 <span class="data-card-unit">%</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据源管理 -->
            <div class="data-source-panel">
                <div class="source-card">
                    <div class="source-header">
                        <div class="source-title">
                            <i class="fas fa-plug"></i>
                            数据接入源
                        </div>
                        <div class="source-status">
                            <div class="status-dot status-online"></div>
                            <span>8/10 在线</span>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: var(--spacing-sm);">
                        MQTT/OPC UA/Modbus协议统一接入
                    </div>
                    <div class="source-metrics">
                        <div class="metric-item">
                            <div class="metric-value">156</div>
                            <div class="metric-label">传感器</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">42</div>
                            <div class="metric-label">设备</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">8</div>
                            <div class="metric-label">系统</div>
                        </div>
                    </div>
                </div>

                <div class="source-card">
                    <div class="source-header">
                        <div class="source-title">
                            <i class="fas fa-server"></i>
                            数据处理引擎
                        </div>
                        <div class="source-status">
                            <div class="status-dot status-online"></div>
                            <span>运行正常</span>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: var(--spacing-sm);">
                        Kafka + Flink + InfluxDB + MinIO
                    </div>
                    <div class="source-metrics">
                        <div class="metric-item">
                            <div class="metric-value">2.4K</div>
                            <div class="metric-label">处理速度/秒</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">99.8</div>
                            <div class="metric-label">可用性%</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">15ms</div>
                            <div class="metric-label">延迟</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI模型管理 -->
            <div class="model-management">
                <h3 class="card-title mb-3">
                    <i class="fas fa-brain"></i>
                    AI模型管理与推理服务
                </h3>
                <div class="model-grid">
                    <div class="model-card">
                        <div class="model-header">
                            <div class="model-name">粉尘识别模型</div>
                            <div class="model-version">v2.1</div>
                        </div>
                        <div class="model-info">YOLO + U-Net 深度学习模型</div>
                        <div class="model-metrics">
                            <span class="model-accuracy">准确率: 92.5%</span>
                            <span class="model-latency">延迟: 45ms</span>
                        </div>
                    </div>

                    <div class="model-card">
                        <div class="model-header">
                            <div class="model-name">瓦斯浓度预测</div>
                            <div class="model-version">v1.8</div>
                        </div>
                        <div class="model-info">LSTM 时序预测模型</div>
                        <div class="model-metrics">
                            <span class="model-accuracy">准确率: 89.3%</span>
                            <span class="model-latency">延迟: 12ms</span>
                        </div>
                    </div>

                    <div class="model-card">
                        <div class="model-header">
                            <div class="model-name">设备异常检测</div>
                            <div class="model-version">v3.0</div>
                        </div>
                        <div class="model-info">孤立森林 + Autoencoder</div>
                        <div class="model-metrics">
                            <span class="model-accuracy">准确率: 95.1%</span>
                            <span class="model-latency">延迟: 8ms</span>
                        </div>
                    </div>

                    <div class="model-card">
                        <div class="model-header">
                            <div class="model-name">人员行为识别</div>
                            <div class="model-version">v1.5</div>
                        </div>
                        <div class="model-info">CNN + RNN 行为分析</div>
                        <div class="model-metrics">
                            <span class="model-accuracy">准确率: 87.8%</span>
                            <span class="model-latency">延迟: 35ms</span>
                        </div>
                    </div>

                    <div class="model-card">
                        <div class="model-header">
                            <div class="model-name">水位预测模型</div>
                            <div class="model-version">v2.3</div>
                        </div>
                        <div class="model-info">LSTM + 多元回归</div>
                        <div class="model-metrics">
                            <span class="model-accuracy">准确率: 91.7%</span>
                            <span class="model-latency">延迟: 18ms</span>
                        </div>
                    </div>

                    <div class="model-card">
                        <div class="model-header">
                            <div class="model-name">故障诊断模型</div>
                            <div class="model-version">v1.9</div>
                        </div>
                        <div class="model-info">随机森林 + SVM</div>
                        <div class="model-metrics">
                            <span class="model-accuracy">准确率: 93.4%</span>
                            <span class="model-latency">延迟: 22ms</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能分析仪表板 -->
            <div class="analytics-dashboard">
                <div class="chart-panel">
                    <h3 class="card-title">
                        <i class="fas fa-chart-area"></i>
                        数据流量与处理性能
                    </h3>
                    <div class="chart-container">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line" style="font-size: 48px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">
                        实时监控数据接入量、处理延迟、系统负载等关键指标
                    </div>
                </div>

                <div class="insight-panel">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-lightbulb"></i>
                        智能洞察
                    </h3>
                    <div class="insight-item">
                        <div class="insight-title">数据质量提升</div>
                        <div class="insight-content">
                            通过数据清洗和标准化，数据质量评分提升至92%，为AI模型提供更可靠的训练数据。
                        </div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-title">模型性能优化</div>
                        <div class="insight-content">
                            粉尘识别模型经过重新训练，准确率从89.2%提升至92.5%，误报率降低35%。
                        </div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-title">预测精度改进</div>
                        <div class="insight-content">
                            瓦斯浓度预测模型引入气象数据后，预测精度提升8%，预警时间提前15分钟。
                        </div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-title">系统负载均衡</div>
                        <div class="insight-content">
                            通过智能负载均衡，系统处理能力提升25%，响应时间缩短至平均15ms。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统架构 -->
            <div class="system-architecture">
                <h3 class="card-title mb-3">
                    <i class="fas fa-sitemap"></i>
                    数据中台系统架构
                </h3>
                <div class="architecture-diagram">
                    <!-- 数据接入层 -->
                    <div class="arch-component" style="top: 10%; left: 5%; width: 15%;">
                        传感器数据
                    </div>
                    <div class="arch-component" style="top: 30%; left: 5%; width: 15%;">
                        设备数据
                    </div>
                    <div class="arch-component" style="top: 50%; left: 5%; width: 15%;">
                        视频数据
                    </div>

                    <!-- 数据处理层 -->
                    <div class="arch-component" style="top: 20%; left: 30%; width: 15%;">
                        Kafka
                    </div>
                    <div class="arch-component" style="top: 40%; left: 30%; width: 15%;">
                        Flink
                    </div>

                    <!-- 数据存储层 -->
                    <div class="arch-component" style="top: 10%; left: 55%; width: 15%;">
                        InfluxDB
                    </div>
                    <div class="arch-component" style="top: 30%; left: 55%; width: 15%;">
                        MinIO
                    </div>
                    <div class="arch-component" style="top: 50%; left: 55%; width: 15%;">
                        Redis
                    </div>

                    <!-- AI服务层 -->
                    <div class="arch-component" style="top: 20%; left: 80%; width: 15%;">
                        模型推理
                    </div>
                    <div class="arch-component" style="top: 40%; left: 80%; width: 15%;">
                        智能分析
                    </div>

                    <!-- 连接箭头 -->
                    <div class="arch-arrow" style="top: 25%; left: 22%;">→</div>
                    <div class="arch-arrow" style="top: 35%; left: 22%;">→</div>
                    <div class="arch-arrow" style="top: 55%; left: 22%;">→</div>
                    <div class="arch-arrow" style="top: 25%; left: 47%;">→</div>
                    <div class="arch-arrow" style="top: 45%; left: 47%;">→</div>
                    <div class="arch-arrow" style="top: 15%; left: 72%;">→</div>
                    <div class="arch-arrow" style="top: 35%; left: 72%;">→</div>
                    <div class="arch-arrow" style="top: 55%; left: 72%;">→</div>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="performance-metrics">
                <div class="perf-card">
                    <div class="perf-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="perf-value" id="throughput">2,450</div>
                    <div class="perf-label">数据吞吐量 (条/秒)</div>
                    <div class="perf-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +12%
                    </div>
                </div>

                <div class="perf-card">
                    <div class="perf-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="perf-value" id="latency">15</div>
                    <div class="perf-label">平均延迟 (ms)</div>
                    <div class="perf-trend trend-down">
                        <i class="fas fa-arrow-down"></i> -8%
                    </div>
                </div>

                <div class="perf-card">
                    <div class="perf-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="perf-value" id="cpuUsage">68</div>
                    <div class="perf-label">CPU使用率 (%)</div>
                    <div class="perf-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +3%
                    </div>
                </div>

                <div class="perf-card">
                    <div class="perf-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="perf-value" id="memUsage">72</div>
                    <div class="perf-label">内存使用率 (%)</div>
                    <div class="perf-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +5%
                    </div>
                </div>

                <div class="perf-card">
                    <div class="perf-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="perf-value" id="diskUsage">45</div>
                    <div class="perf-label">磁盘使用率 (%)</div>
                    <div class="perf-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +2%
                    </div>
                </div>

                <div class="perf-card">
                    <div class="perf-icon" style="background: linear-gradient(135deg, #64748b, #475569);">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <div class="perf-value" id="networkIO">1.2</div>
                    <div class="perf-label">网络IO (GB/s)</div>
                    <div class="perf-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +15%
                    </div>
                </div>
            </div>
        `;

        // 架构组件点击事件
        document.querySelectorAll('.arch-component').forEach(component => {
            component.addEventListener('click', (e) => {
                const componentName = e.target.textContent.trim();
                MiningAI.showNotification(`${componentName} 组件详情`, 'info');
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('totalData', 1.0, 1.5, 'TB', 1);
        MiningAI.simulateRealTimeData('dataStream', 2200, 2800, '条/秒', 0);
        MiningAI.simulateRealTimeData('modelCount', 14, 16, '个', 0);
        MiningAI.simulateRealTimeData('accuracy', 92, 96, '%', 1);

        // 性能指标实时更新
        MiningAI.simulateRealTimeData('throughput', 2200, 2800, '', 0);
        MiningAI.simulateRealTimeData('latency', 10, 25, '', 0);
        MiningAI.simulateRealTimeData('cpuUsage', 60, 80, '', 0);
        MiningAI.simulateRealTimeData('memUsage', 65, 85, '', 0);
        MiningAI.simulateRealTimeData('diskUsage', 40, 55, '', 0);
        MiningAI.simulateRealTimeData('networkIO', 0.8, 1.8, '', 1);

        // 模拟模型性能更新
        setInterval(() => {
            const modelCards = document.querySelectorAll('.model-card');
            modelCards.forEach(card => {
                const accuracyElement = card.querySelector('.model-accuracy');
                const latencyElement = card.querySelector('.model-latency');

                if (accuracyElement && latencyElement) {
                    const currentAccuracy = parseFloat(accuracyElement.textContent.match(/[\d.]+/)[0]);
                    const currentLatency = parseFloat(latencyElement.textContent.match(/[\d.]+/)[0]);

                    const newAccuracy = (currentAccuracy + (Math.random() - 0.5) * 2).toFixed(1);
                    const newLatency = Math.max(5, currentLatency + (Math.random() - 0.5) * 10).toFixed(0);

                    accuracyElement.textContent = `准确率: ${newAccuracy}%`;
                    latencyElement.textContent = `延迟: ${newLatency}ms`;
                }
            });
        }, 5000);

        // 显示系统启动消息
        setTimeout(() => {
            MiningAI.showNotification('数据中台系统运行正常', 'success');
        }, 1000);
    </script>
</body>
</html>
