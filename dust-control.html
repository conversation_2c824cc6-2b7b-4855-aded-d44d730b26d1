<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粉尘监控 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .monitoring-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .video-monitor {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: var(--radius-md);
            overflow: hidden;
            aspect-ratio: 16/9;
            margin-bottom: var(--spacing-sm);
        }

        .video-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }

        .video-overlay {
            position: absolute;
            top: var(--spacing-sm);
            left: var(--spacing-sm);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 12px;
        }

        .video-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-group {
            display: flex;
            gap: var(--spacing-xs);
        }

        .control-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: var(--bg-color);
        }

        .control-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .sensor-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .sensor-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .sensor-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: var(--spacing-xs);
        }

        .sensor-title {
            font-weight: 600;
            font-size: 14px;
            color: var(--text-primary);
        }

        .sensor-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
            text-align: center;
            margin: var(--spacing-xs) 0;
        }

        .sensor-unit {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .sensor-chart {
            height: 60px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin-top: var(--spacing-xs);
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
            animation: chartPulse 2s ease-in-out infinite;
        }

        @keyframes chartPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .dust-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .analysis-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .analysis-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .analysis-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .analysis-content {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        .control-system {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .control-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            background: var(--bg-color);
            border-radius: var(--radius-sm);
        }

        .control-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .switch.active {
            background: var(--success-color);
        }

        .switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .switch.active::after {
            transform: translateX(20px);
        }

        .alert-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-top: var(--spacing-md);
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .alert-item:last-child {
            margin-bottom: 0;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning-color);
        }

        .alert-normal {
            background: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('dust-control.html', '粉尘监控', ['首页', '粉尘监控']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 监控主界面 -->
            <div class="monitoring-grid">
                <!-- 视频监控区域 -->
                <div class="video-monitor">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-video"></i>
                        实时视频监控 - 3号工作面
                    </h3>
                    <div class="video-container">
                        <div class="video-placeholder">
                            <i class="fas fa-video" style="font-size: 48px;"></i>
                        </div>
                        <div class="video-overlay">
                            <i class="fas fa-circle" style="color: #ef4444; animation: blink 1s infinite;"></i>
                            实时监控中
                        </div>
                    </div>
                    <div class="video-controls">
                        <div class="control-group">
                            <button class="control-btn active">
                                <i class="fas fa-play"></i>
                                播放
                            </button>
                            <button class="control-btn">
                                <i class="fas fa-pause"></i>
                                暂停
                            </button>
                            <button class="control-btn">
                                <i class="fas fa-expand"></i>
                                全屏
                            </button>
                        </div>
                        <div class="control-group">
                            <button class="control-btn">
                                <i class="fas fa-camera"></i>
                                截图
                            </button>
                            <button class="control-btn">
                                <i class="fas fa-download"></i>
                                录制
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 传感器数据面板 -->
                <div class="sensor-panel">
                    <div class="sensor-card">
                        <div class="sensor-header">
                            <span class="sensor-title">粉尘浓度</span>
                            <span class="status-indicator status-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                偏高
                            </span>
                        </div>
                        <div class="sensor-value" id="dustConcentration">
                            15.2 <span class="sensor-unit">mg/m³</span>
                        </div>
                        <div class="sensor-chart">
                            <div class="chart-line"></div>
                        </div>
                    </div>

                    <div class="sensor-card">
                        <div class="sensor-header">
                            <span class="sensor-title">PM2.5</span>
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </span>
                        </div>
                        <div class="sensor-value" id="pm25">
                            8.5 <span class="sensor-unit">μg/m³</span>
                        </div>
                        <div class="sensor-chart">
                            <div class="chart-line"></div>
                        </div>
                    </div>

                    <div class="sensor-card">
                        <div class="sensor-header">
                            <span class="sensor-title">PM10</span>
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </span>
                        </div>
                        <div class="sensor-value" id="pm10">
                            12.3 <span class="sensor-unit">μg/m³</span>
                        </div>
                        <div class="sensor-chart">
                            <div class="chart-line"></div>
                        </div>
                    </div>

                    <div class="sensor-card">
                        <div class="sensor-header">
                            <span class="sensor-title">能见度</span>
                            <span class="status-indicator status-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                较低
                            </span>
                        </div>
                        <div class="sensor-value" id="visibility">
                            25 <span class="sensor-unit">米</span>
                        </div>
                        <div class="sensor-chart">
                            <div class="chart-line"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI分析结果 -->
            <div class="dust-analysis">
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="analysis-title">AI粉尘识别</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>识别结果：</strong>检测到高浓度粉尘区域</p>
                        <p><strong>置信度：</strong>92.5%</p>
                        <p><strong>影响范围：</strong>约15平方米</p>
                        <p><strong>建议操作：</strong>启动喷淋系统</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="analysis-title">趋势预测</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>预测模型：</strong>LSTM时序预测</p>
                        <p><strong>未来1小时：</strong>浓度将上升至18mg/m³</p>
                        <p><strong>峰值时间：</strong>预计35分钟后</p>
                        <p><strong>风险等级：</strong>中等风险</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="analysis-title">分布分析</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>主要来源：</strong>掘进工作面</p>
                        <p><strong>扩散方向：</strong>东南方向</p>
                        <p><strong>影响区域：</strong>3个监测点</p>
                        <p><strong>扩散速度：</strong>0.8m/s</p>
                    </div>
                </div>
            </div>

            <!-- 智能抑尘控制系统 -->
            <div class="control-system">
                <h3 class="card-title">
                    <i class="fas fa-cogs"></i>
                    智能抑尘控制系统
                </h3>
                <div class="control-grid">
                    <div class="control-item">
                        <span class="control-label">自动喷淋</span>
                        <div class="switch active" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-item">
                        <span class="control-label">风机联动</span>
                        <div class="switch active" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-item">
                        <span class="control-label">雾化降尘</span>
                        <div class="switch" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-item">
                        <span class="control-label">智能预警</span>
                        <div class="switch active" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-item">
                        <span class="control-label">数据记录</span>
                        <div class="switch active" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-item">
                        <span class="control-label">远程推送</span>
                        <div class="switch active" onclick="toggleSwitch(this)"></div>
                    </div>
                </div>
            </div>

            <!-- 报警和事件记录 -->
            <div class="alert-panel">
                <h3 class="card-title mb-3">
                    <i class="fas fa-bell"></i>
                    实时报警与事件记录
                </h3>
                <div class="alert-item alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>粉尘浓度超标</strong> - 3号工作面粉尘浓度达到15.2mg/m³，超过安全阈值
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            2分钟前 | 已启动自动喷淋系统
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-normal">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>喷淋系统启动</strong> - 自动喷淋系统已启动，开始降尘作业
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            3分钟前 | 系统自动执行
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-normal">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>数据采集正常</strong> - 所有传感器数据采集正常，系统运行稳定
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            15分钟前 | 定期检查
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 开关切换功能
        function toggleSwitch(element) {
            element.classList.toggle('active');
            const label = element.parentElement.querySelector('.control-label').textContent;
            const status = element.classList.contains('active') ? '已启用' : '已禁用';
            MiningAI.showNotification(`${label} ${status}`, 'info');
        }

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('dustConcentration', 10, 20, 'mg/m³', 1);
        MiningAI.simulateRealTimeData('pm25', 5, 15, 'μg/m³', 1);
        MiningAI.simulateRealTimeData('pm10', 8, 18, 'μg/m³', 1);
        MiningAI.simulateRealTimeData('visibility', 20, 40, '米', 0);

        // 添加闪烁动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0.3; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
