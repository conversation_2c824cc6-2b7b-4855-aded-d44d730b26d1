<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运维管理 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .ops-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .deployment-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .deployment-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .deployment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .deployment-title {
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .deployment-status {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }

        .node-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .node-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
        }

        .node-item:last-child {
            border-bottom: none;
        }

        .node-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .node-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .node-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .node-specs {
            color: var(--text-secondary);
        }

        .node-metrics {
            text-align: right;
            color: var(--text-secondary);
        }

        .monitoring-dashboard {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-md);
        }

        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .monitor-item {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .monitor-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .monitor-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .monitor-trend {
            font-size: 10px;
            margin-top: 2px;
        }

        .model-deployment {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .model-list {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
        }

        .model-item:hover {
            background: var(--bg-color);
        }

        .model-item:last-child {
            border-bottom: none;
        }

        .model-info {
            flex: 1;
        }

        .model-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .model-version {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .model-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 12px;
        }

        .model-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .update-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .update-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .update-item:last-child {
            border-bottom: none;
        }

        .update-info {
            flex: 1;
        }

        .update-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 13px;
        }

        .update-time {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .update-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-failed {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .log-viewer {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            height: 200px;
            overflow-y: auto;
            margin-top: var(--spacing-sm);
        }

        .log-line {
            margin-bottom: 2px;
            white-space: nowrap;
        }

        .log-timestamp {
            color: #888;
        }

        .log-level-info {
            color: #00ff00;
        }

        .log-level-warn {
            color: #ffff00;
        }

        .log-level-error {
            color: #ff0000;
        }

        .disaster-recovery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
        }

        .recovery-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .recovery-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .recovery-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .recovery-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .recovery-content {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        .backup-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs);
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: 4px 0;
            font-size: 12px;
        }

        .backup-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .backup-time {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('operations.html', '运维管理', ['首页', '运维管理']);
        
        // 页面内容
        pageContent.innerHTML = `
            <!-- 运维概览 -->
            <div class="ops-overview">
                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">边缘节点</div>
                                <div class="data-card-value" id="edgeNodes">12 <span class="data-card-unit">个</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">云端服务</div>
                                <div class="data-card-value" id="cloudServices">8 <span class="data-card-unit">个</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">部署模型</div>
                                <div class="data-card-value" id="deployedModels">15 <span class="data-card-unit">个</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">系统可用性</div>
                                <div class="data-card-value" id="availability">99.8 <span class="data-card-unit">%</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 边云协同部署 -->
            <div class="deployment-panel">
                <div class="deployment-card">
                    <div class="deployment-header">
                        <div class="deployment-title">
                            <i class="fas fa-microchip"></i>
                            边缘计算节点
                        </div>
                        <div class="deployment-status">
                            <div class="status-dot status-online"></div>
                            <span>10/12 在线</span>
                        </div>
                    </div>
                    <div class="node-list">
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">Edge-Node-01</div>
                                    <div class="node-specs">工控机 | 8核16G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 65% | 内存: 72%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">Edge-Node-02</div>
                                    <div class="node-specs">工控机 | 8核16G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 58% | 内存: 68%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--warning-color);"></div>
                                <div>
                                    <div class="node-name">Edge-Node-03</div>
                                    <div class="node-specs">网关 | 4核8G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 85% | 内存: 92%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">Edge-Node-04</div>
                                    <div class="node-specs">工控机 | 8核16G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 42% | 内存: 55%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--danger-color);"></div>
                                <div>
                                    <div class="node-name">Edge-Node-05</div>
                                    <div class="node-specs">网关 | 4核8G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                离线 | 网络故障
                            </div>
                        </div>
                    </div>
                </div>

                <div class="deployment-card">
                    <div class="deployment-header">
                        <div class="deployment-title">
                            <i class="fas fa-cloud"></i>
                            云端服务集群
                        </div>
                        <div class="deployment-status">
                            <div class="status-dot status-online"></div>
                            <span>8/8 运行</span>
                        </div>
                    </div>
                    <div class="node-list">
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">AI-Server-01</div>
                                    <div class="node-specs">GPU服务器 | 32核64G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                GPU: 78% | 内存: 65%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">Data-Server-01</div>
                                    <div class="node-specs">数据库服务器 | 16核32G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 45% | 内存: 58%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">Web-Server-01</div>
                                    <div class="node-specs">Web服务器 | 8核16G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 35% | 内存: 42%
                            </div>
                        </div>
                        <div class="node-item">
                            <div class="node-info">
                                <div class="node-status" style="background: var(--success-color);"></div>
                                <div>
                                    <div class="node-name">Cache-Server-01</div>
                                    <div class="node-specs">缓存服务器 | 8核32G</div>
                                </div>
                            </div>
                            <div class="node-metrics">
                                CPU: 28% | 内存: 75%
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统监控仪表板 -->
            <div class="monitoring-dashboard">
                <h3 class="card-title">
                    <i class="fas fa-chart-line"></i>
                    系统运行监控
                </h3>
                <div class="monitoring-grid">
                    <div class="monitor-item">
                        <div class="monitor-value" id="totalRequests">15,420</div>
                        <div class="monitor-label">总请求数</div>
                        <div class="monitor-trend" style="color: var(--success-color);">
                            <i class="fas fa-arrow-up"></i> +8%
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-value" id="responseTime">125ms</div>
                        <div class="monitor-label">平均响应时间</div>
                        <div class="monitor-trend" style="color: var(--danger-color);">
                            <i class="fas fa-arrow-up"></i> +5%
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-value" id="errorRate">0.12%</div>
                        <div class="monitor-label">错误率</div>
                        <div class="monitor-trend" style="color: var(--success-color);">
                            <i class="fas fa-arrow-down"></i> -15%
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-value" id="throughput">2,450</div>
                        <div class="monitor-label">吞吐量/秒</div>
                        <div class="monitor-trend" style="color: var(--success-color);">
                            <i class="fas fa-arrow-up"></i> +12%
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-value" id="activeUsers">127</div>
                        <div class="monitor-label">活跃用户</div>
                        <div class="monitor-trend" style="color: var(--success-color);">
                            <i class="fas fa-arrow-up"></i> +3%
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-value" id="dataVolume">1.2TB</div>
                        <div class="monitor-label">数据处理量</div>
                        <div class="monitor-trend" style="color: var(--success-color);">
                            <i class="fas fa-arrow-up"></i> +18%
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型部署管理 -->
            <div class="model-deployment">
                <div class="model-list">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-brain"></i>
                        AI模型部署管理
                    </h3>
                    <div class="model-item">
                        <div class="model-info">
                            <div class="model-name">粉尘识别模型</div>
                            <div class="model-version">v2.1 | 边缘节点部署</div>
                        </div>
                        <div class="model-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                运行中
                            </span>
                        </div>
                        <div class="model-actions">
                            <button class="action-btn">更新</button>
                            <button class="action-btn">重启</button>
                            <button class="action-btn">停止</button>
                        </div>
                    </div>
                    <div class="model-item">
                        <div class="model-info">
                            <div class="model-name">瓦斯浓度预测</div>
                            <div class="model-version">v1.8 | 云端部署</div>
                        </div>
                        <div class="model-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                运行中
                            </span>
                        </div>
                        <div class="model-actions">
                            <button class="action-btn">更新</button>
                            <button class="action-btn">重启</button>
                            <button class="action-btn">停止</button>
                        </div>
                    </div>
                    <div class="model-item">
                        <div class="model-info">
                            <div class="model-name">设备异常检测</div>
                            <div class="model-version">v3.0 | 混合部署</div>
                        </div>
                        <div class="model-status">
                            <span class="status-indicator status-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                更新中
                            </span>
                        </div>
                        <div class="model-actions">
                            <button class="action-btn" disabled>更新</button>
                            <button class="action-btn">重启</button>
                            <button class="action-btn">停止</button>
                        </div>
                    </div>
                    <div class="model-item">
                        <div class="model-info">
                            <div class="model-name">人员行为识别</div>
                            <div class="model-version">v1.5 | 边缘节点部署</div>
                        </div>
                        <div class="model-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                运行中
                            </span>
                        </div>
                        <div class="model-actions">
                            <button class="action-btn">更新</button>
                            <button class="action-btn">重启</button>
                            <button class="action-btn">停止</button>
                        </div>
                    </div>
                    <div class="model-item">
                        <div class="model-info">
                            <div class="model-name">水位预测模型</div>
                            <div class="model-version">v2.3 | 云端部署</div>
                        </div>
                        <div class="model-status">
                            <span class="status-indicator status-danger">
                                <i class="fas fa-times-circle"></i>
                                故障
                            </span>
                        </div>
                        <div class="model-actions">
                            <button class="action-btn">更新</button>
                            <button class="action-btn">重启</button>
                            <button class="action-btn">诊断</button>
                        </div>
                    </div>
                </div>

                <div class="update-panel">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-download"></i>
                        OTA更新记录
                    </h3>
                    <div class="update-item">
                        <div class="update-info">
                            <div class="update-title">设备异常检测模型 v3.0</div>
                            <div class="update-time">2024-01-15 14:30</div>
                        </div>
                        <div class="update-status status-pending">进行中</div>
                    </div>
                    <div class="update-item">
                        <div class="update-info">
                            <div class="update-title">粉尘识别模型 v2.1</div>
                            <div class="update-time">2024-01-14 09:15</div>
                        </div>
                        <div class="update-status status-success">成功</div>
                    </div>
                    <div class="update-item">
                        <div class="update-info">
                            <div class="update-title">系统固件更新</div>
                            <div class="update-time">2024-01-13 16:45</div>
                        </div>
                        <div class="update-status status-success">成功</div>
                    </div>
                    <div class="update-item">
                        <div class="update-info">
                            <div class="update-title">瓦斯预测模型 v1.7</div>
                            <div class="update-time">2024-01-12 11:20</div>
                        </div>
                        <div class="update-status status-failed">失败</div>
                    </div>
                    <div class="update-item">
                        <div class="update-info">
                            <div class="update-title">人员识别模型 v1.5</div>
                            <div class="update-time">2024-01-11 08:30</div>
                        </div>
                        <div class="update-status status-success">成功</div>
                    </div>
                </div>
            </div>

            <!-- 系统日志查看器 -->
            <div class="log-viewer">
                <h3 class="card-title">
                    <i class="fas fa-file-alt"></i>
                    系统运行日志
                </h3>
                <div class="log-container" id="logContainer">
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:30:25]</span>
                        <span class="log-level-info">[INFO]</span>
                        系统启动完成，所有服务正常运行
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:30:28]</span>
                        <span class="log-level-info">[INFO]</span>
                        边缘节点 Edge-Node-01 连接成功
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:30:32]</span>
                        <span class="log-level-info">[INFO]</span>
                        AI模型推理服务启动，加载15个模型
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:30:45]</span>
                        <span class="log-level-warn">[WARN]</span>
                        边缘节点 Edge-Node-03 CPU使用率过高: 85%
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:31:02]</span>
                        <span class="log-level-info">[INFO]</span>
                        数据处理管道正常，当前吞吐量: 2450条/秒
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:31:15]</span>
                        <span class="log-level-error">[ERROR]</span>
                        边缘节点 Edge-Node-05 连接丢失，网络故障
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:31:28]</span>
                        <span class="log-level-info">[INFO]</span>
                        自动故障转移启动，流量重定向至其他节点
                    </div>
                    <div class="log-line">
                        <span class="log-timestamp">[2024-01-15 15:31:45]</span>
                        <span class="log-level-warn">[WARN]</span>
                        水位预测模型响应超时，正在重启服务
                    </div>
                </div>
            </div>

            <!-- 容灾恢复 -->
            <div class="disaster-recovery">
                <div class="recovery-card">
                    <div class="recovery-header">
                        <div class="recovery-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="recovery-title">高可用性保障</div>
                    </div>
                    <div class="recovery-content">
                        <p><strong>系统可用性：</strong>99.8%</p>
                        <p><strong>故障转移时间：</strong>&lt; 30秒</p>
                        <p><strong>数据同步延迟：</strong>&lt; 5秒</p>
                        <p><strong>备份节点：</strong>3个活跃备份</p>
                        <br>
                        <p><strong>关键系统独立运行：</strong></p>
                        <p>• 排水系统：边缘独立闭环</p>
                        <p>• 瓦斯监测：本地备份推理</p>
                        <p>• 人员定位：离线缓存机制</p>
                    </div>
                </div>

                <div class="recovery-card">
                    <div class="recovery-header">
                        <div class="recovery-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="recovery-title">数据备份策略</div>
                    </div>
                    <div class="recovery-content">
                        <p><strong>备份策略：</strong></p>
                        <div class="backup-status">
                            <span class="backup-name">全量备份</span>
                            <span class="backup-time">每日 02:00</span>
                        </div>
                        <div class="backup-status">
                            <span class="backup-name">增量备份</span>
                            <span class="backup-time">每4小时</span>
                        </div>
                        <div class="backup-status">
                            <span class="backup-name">实时同步</span>
                            <span class="backup-time">持续进行</span>
                        </div>
                        <br>
                        <p><strong>恢复能力：</strong></p>
                        <p>• RTO (恢复时间目标): &lt; 1小时</p>
                        <p>• RPO (恢复点目标): &lt; 15分钟</p>
                    </div>
                </div>

                <div class="recovery-card">
                    <div class="recovery-header">
                        <div class="recovery-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="recovery-title">运维自动化</div>
                    </div>
                    <div class="recovery-content">
                        <p><strong>自动化运维功能：</strong></p>
                        <p>• 健康检查：每30秒</p>
                        <p>• 自动重启：故障服务</p>
                        <p>• 负载均衡：动态调整</p>
                        <p>• 资源扩缩：按需分配</p>
                        <br>
                        <p><strong>监控告警：</strong></p>
                        <p>• 系统指标监控</p>
                        <p>• 异常自动告警</p>
                        <p>• 多渠道通知</p>
                        <p>• 故障自动处理</p>
                    </div>
                </div>
            </div>
        `;

        // 模型操作按钮事件
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (e.target.disabled) return;

                const action = e.target.textContent;
                const modelName = e.target.closest('.model-item').querySelector('.model-name').textContent;
                MiningAI.showNotification(`${modelName} ${action}操作已执行`, 'info');
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('edgeNodes', 10, 12, '个', 0);
        MiningAI.simulateRealTimeData('cloudServices', 7, 8, '个', 0);
        MiningAI.simulateRealTimeData('deployedModels', 14, 16, '个', 0);
        MiningAI.simulateRealTimeData('availability', 99.5, 99.9, '%', 1);

        // 监控指标实时更新
        MiningAI.simulateRealTimeData('totalRequests', 14000, 17000, '', 0);
        MiningAI.simulateRealTimeData('responseTime', 100, 150, 'ms', 0);
        MiningAI.simulateRealTimeData('errorRate', 0.05, 0.20, '%', 2);
        MiningAI.simulateRealTimeData('throughput', 2200, 2800, '', 0);
        MiningAI.simulateRealTimeData('activeUsers', 120, 135, '', 0);
        MiningAI.simulateRealTimeData('dataVolume', 1.0, 1.5, 'TB', 1);

        // 模拟日志滚动更新
        const logContainer = document.getElementById('logContainer');
        const logLevels = ['INFO', 'WARN', 'ERROR'];
        const logMessages = [
            '数据处理管道正常运行',
            '模型推理服务响应正常',
            '边缘节点心跳检测正常',
            '系统负载均衡调整',
            '缓存清理完成',
            '数据备份任务执行',
            '网络连接检查完成',
            '服务健康检查通过'
        ];

        setInterval(() => {
            const now = new Date();
            const timestamp = now.toISOString().slice(0, 19).replace('T', ' ');
            const level = logLevels[Math.floor(Math.random() * logLevels.length)];
            const message = logMessages[Math.floor(Math.random() * logMessages.length)];

            const logLine = document.createElement('div');
            logLine.className = 'log-line';
            logLine.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level.toLowerCase()}">[${level}]</span>
                ${message}
            `;

            logContainer.appendChild(logLine);
            logContainer.scrollTop = logContainer.scrollHeight;

            // 保持最多50行日志
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }, 3000);

        // 显示系统状态消息
        setTimeout(() => {
            MiningAI.showNotification('边云协同系统运行正常', 'success');
        }, 1000);
    </script>
</body>
</html>
