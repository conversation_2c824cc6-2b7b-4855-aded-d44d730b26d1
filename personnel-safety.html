<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人员安全 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .safety-overview {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .location-map {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .map-container {
            height: 400px;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .map-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .personnel-marker {
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: white;
            font-weight: bold;
        }

        .marker-normal { background: var(--success-color); }
        .marker-warning { background: var(--warning-color); }
        .marker-danger { background: var(--danger-color); }
        .marker-offline { background: var(--text-muted); }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
        }

        .zone-area {
            position: absolute;
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: var(--radius-sm);
            background: rgba(255, 255, 255, 0.1);
        }

        .zone-label {
            position: absolute;
            top: 4px;
            left: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
            font-size: 10px;
        }

        .personnel-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
            display: none;
            max-width: 200px;
        }

        .map-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-group {
            display: flex;
            gap: var(--spacing-xs);
        }

        .control-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: var(--bg-color);
        }

        .control-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .personnel-list {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            max-height: 500px;
            overflow-y: auto;
        }

        .personnel-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
        }

        .personnel-item:hover {
            background: var(--bg-color);
        }

        .personnel-item:last-child {
            border-bottom: none;
        }

        .personnel-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .personnel-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .personnel-details {
            flex: 1;
        }

        .personnel-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .personnel-role {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .personnel-location {
            font-size: 11px;
            color: var(--text-muted);
        }

        .personnel-status {
            text-align: right;
        }

        .status-time {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .behavior-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .analysis-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .analysis-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .analysis-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .analysis-content {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        .video-monitor {
            height: 200px;
            background: #000;
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .video-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        .video-overlay {
            position: absolute;
            top: var(--spacing-xs);
            left: var(--spacing-xs);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 10px;
        }

        .health-monitoring {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .health-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .health-item {
            background: var(--bg-color);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            text-align: center;
        }

        .health-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .health-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .alert-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-top: var(--spacing-md);
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .alert-item:last-child {
            margin-bottom: 0;
        }

        .alert-critical {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning-color);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            border-left: 4px solid var(--info-color);
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('personnel-safety.html', '人员安全', ['首页', '人员安全']);

        // 页面内容
        pageContent.innerHTML = `
            <!-- 人员安全概览 -->
            <div class="safety-overview">
                <!-- 人员定位地图 -->
                <div class="location-map">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-map-marked-alt"></i>
                        实时人员定位地图
                    </h3>
                    <div class="map-container">
                        <div class="map-overlay"></div>

                        <!-- 区域划分 -->
                        <div class="zone-area" style="top: 10%; left: 10%; width: 35%; height: 25%;">
                            <div class="zone-label">主巷道区域</div>
                        </div>
                        <div class="zone-area" style="top: 10%; right: 10%; width: 35%; height: 25%;">
                            <div class="zone-label">1号工作面</div>
                        </div>
                        <div class="zone-area" style="top: 45%; left: 10%; width: 35%; height: 25%;">
                            <div class="zone-label">2号工作面</div>
                        </div>
                        <div class="zone-area" style="top: 45%; right: 10%; width: 35%; height: 25%;">
                            <div class="zone-label">3号工作面</div>
                        </div>
                        <div class="zone-area" style="bottom: 10%; left: 20%; width: 60%; height: 20%;">
                            <div class="zone-label">运输巷道</div>
                        </div>

                        <!-- 人员标记点 -->
                        <div class="personnel-marker marker-normal" style="top: 20%; left: 25%;"
                             data-name="张三" data-role="班长" data-location="主巷道" data-status="正常" data-time="2分钟前">1</div>
                        <div class="personnel-marker marker-normal" style="top: 25%; left: 70%;"
                             data-name="李四" data-role="采煤工" data-location="1号工作面" data-status="正常" data-time="1分钟前">2</div>
                        <div class="personnel-marker marker-warning" style="top: 55%; left: 30%;"
                             data-name="王五" data-role="掘进工" data-location="2号工作面" data-status="未戴安全帽" data-time="刚刚">3</div>
                        <div class="personnel-marker marker-normal" style="top: 60%; right: 25%;"
                             data-name="赵六" data-role="安全员" data-location="3号工作面" data-status="正常" data-time="3分钟前">4</div>
                        <div class="personnel-marker marker-danger" style="bottom: 25%; left: 45%;"
                             data-name="孙七" data-role="维修工" data-location="运输巷道" data-status="跌倒报警" data-time="5分钟前">5</div>
                        <div class="personnel-marker marker-normal" style="top: 30%; left: 15%;"
                             data-name="周八" data-role="电工" data-location="主巷道" data-status="正常" data-time="4分钟前">6</div>
                        <div class="personnel-marker marker-offline" style="top: 50%; left: 80%;"
                             data-name="吴九" data-role="机械工" data-location="1号工作面" data-status="设备离线" data-time="10分钟前">7</div>

                        <div class="personnel-tooltip" id="personnelTooltip"></div>
                    </div>
                    <div class="map-controls">
                        <div class="control-group">
                            <button class="control-btn active">全部人员</button>
                            <button class="control-btn">正常状态</button>
                            <button class="control-btn">异常状态</button>
                            <button class="control-btn">离线设备</button>
                        </div>
                        <div class="control-group">
                            <button class="control-btn">
                                <i class="fas fa-search-plus"></i>
                                放大
                            </button>
                            <button class="control-btn">
                                <i class="fas fa-search-minus"></i>
                                缩小
                            </button>
                            <button class="control-btn">
                                <i class="fas fa-expand"></i>
                                全屏
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 人员列表 -->
                <div class="personnel-list">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-users"></i>
                        在线人员列表 (127人)
                    </h3>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--success-color);">张</div>
                            <div class="personnel-details">
                                <div class="personnel-name">张三</div>
                                <div class="personnel-role">班长</div>
                                <div class="personnel-location">主巷道区域</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </span>
                            <div class="status-time">2分钟前</div>
                        </div>
                    </div>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--success-color);">李</div>
                            <div class="personnel-details">
                                <div class="personnel-name">李四</div>
                                <div class="personnel-role">采煤工</div>
                                <div class="personnel-location">1号工作面</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </span>
                            <div class="status-time">1分钟前</div>
                        </div>
                    </div>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--warning-color);">王</div>
                            <div class="personnel-details">
                                <div class="personnel-name">王五</div>
                                <div class="personnel-role">掘进工</div>
                                <div class="personnel-location">2号工作面</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                未戴安全帽
                            </span>
                            <div class="status-time">刚刚</div>
                        </div>
                    </div>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--success-color);">赵</div>
                            <div class="personnel-details">
                                <div class="personnel-name">赵六</div>
                                <div class="personnel-role">安全员</div>
                                <div class="personnel-location">3号工作面</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </span>
                            <div class="status-time">3分钟前</div>
                        </div>
                    </div>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--danger-color);">孙</div>
                            <div class="personnel-details">
                                <div class="personnel-name">孙七</div>
                                <div class="personnel-role">维修工</div>
                                <div class="personnel-location">运输巷道</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-danger">
                                <i class="fas fa-times-circle"></i>
                                跌倒报警
                            </span>
                            <div class="status-time">5分钟前</div>
                        </div>
                    </div>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--success-color);">周</div>
                            <div class="personnel-details">
                                <div class="personnel-name">周八</div>
                                <div class="personnel-role">电工</div>
                                <div class="personnel-location">主巷道区域</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </span>
                            <div class="status-time">4分钟前</div>
                        </div>
                    </div>
                    <div class="personnel-item">
                        <div class="personnel-info">
                            <div class="personnel-avatar" style="background: var(--text-muted);">吴</div>
                            <div class="personnel-details">
                                <div class="personnel-name">吴九</div>
                                <div class="personnel-role">机械工</div>
                                <div class="personnel-location">1号工作面</div>
                            </div>
                        </div>
                        <div class="personnel-status">
                            <span class="status-indicator status-danger">
                                <i class="fas fa-wifi"></i>
                                设备离线
                            </span>
                            <div class="status-time">10分钟前</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行为分析 -->
            <div class="behavior-analysis">
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="analysis-title">视频行为分析</div>
                    </div>
                    <div class="video-monitor">
                        <div class="video-placeholder">
                            <i class="fas fa-video" style="font-size: 32px;"></i>
                        </div>
                        <div class="video-overlay">
                            <i class="fas fa-circle" style="color: #ef4444; animation: blink 1s infinite;"></i>
                            AI分析中
                        </div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>检测结果：</strong></p>
                        <p>• 未戴安全帽：1人 (王五)</p>
                        <p>• 违规进入禁区：0人</p>
                        <p>• 异常行为：1人 (孙七-跌倒)</p>
                        <p>• 睡岗检测：0人</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="analysis-title">活动轨迹分析</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>轨迹统计：</strong></p>
                        <p>• 正常活动轨迹：124人</p>
                        <p>• 异常滞留：2人</p>
                        <p>• 超时作业：1人</p>
                        <p>• 频繁进出：0人</p>
                        <br>
                        <p><strong>热点区域：</strong></p>
                        <p>• 1号工作面：35人</p>
                        <p>• 2号工作面：28人</p>
                        <p>• 3号工作面：32人</p>
                        <p>• 主巷道：32人</p>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <div class="analysis-title">健康状态监测</div>
                    </div>
                    <div class="analysis-content">
                        <p><strong>生理指标监测：</strong></p>
                        <p>• 心率异常：2人</p>
                        <p>• 体温异常：0人</p>
                        <p>• 疲劳预警：3人</p>
                        <p>• 环境适应性：良好</p>
                        <br>
                        <p><strong>建议措施：</strong></p>
                        <p>• 安排心率异常人员休息</p>
                        <p>• 疲劳人员轮班调整</p>
                    </div>
                </div>
            </div>

            <!-- 健康监测面板 -->
            <div class="health-monitoring">
                <h3 class="card-title mb-3">
                    <i class="fas fa-heartbeat"></i>
                    可穿戴设备健康监测
                </h3>
                <div class="health-grid">
                    <div class="health-item">
                        <div class="health-value" id="avgHeartRate">78</div>
                        <div class="health-label">平均心率 (bpm)</div>
                    </div>
                    <div class="health-item">
                        <div class="health-value" id="avgBodyTemp">36.5</div>
                        <div class="health-label">平均体温 (°C)</div>
                    </div>
                    <div class="health-item">
                        <div class="health-value" id="fatigueLevel">15</div>
                        <div class="health-label">疲劳指数 (%)</div>
                    </div>
                    <div class="health-item">
                        <div class="health-value" id="stressLevel">25</div>
                        <div class="health-label">压力指数 (%)</div>
                    </div>
                    <div class="health-item">
                        <div class="health-value" id="oxygenSat">98</div>
                        <div class="health-label">血氧饱和度 (%)</div>
                    </div>
                    <div class="health-item">
                        <div class="health-value" id="activityLevel">65</div>
                        <div class="health-label">活动强度 (%)</div>
                    </div>
                </div>
            </div>

            <!-- 安全报警面板 -->
            <div class="alert-panel">
                <h3 class="card-title mb-3">
                    <i class="fas fa-shield-alt"></i>
                    安全报警与事件记录
                </h3>
                <div class="alert-item alert-critical">
                    <i class="fas fa-times-circle"></i>
                    <div>
                        <strong>人员跌倒报警</strong> - 孙七在运输巷道检测到跌倒，已派遣救援
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            5分钟前 | 已通知医疗队
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>安全防护违规</strong> - 王五在2号工作面未佩戴安全帽
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            刚刚 | 已发送提醒
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-warning">
                    <i class="fas fa-wifi"></i>
                    <div>
                        <strong>设备离线报警</strong> - 吴九的定位设备失去信号
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            10分钟前 | 正在排查
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-info">
                    <i class="fas fa-clock"></i>
                    <div>
                        <strong>区域滞留提醒</strong> - 2人在休息区域滞留超过30分钟
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            15分钟前 | 已发送提醒
                        </div>
                    </div>
                </div>
                <div class="alert-item alert-info">
                    <i class="fas fa-heartbeat"></i>
                    <div>
                        <strong>健康状态提醒</strong> - 3人疲劳指数偏高，建议安排休息
                        <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                            20分钟前 | 已通知班长
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 人员标记悬停提示
        const personnelMarkers = document.querySelectorAll('.personnel-marker');
        const tooltip = document.getElementById('personnelTooltip');

        personnelMarkers.forEach(marker => {
            marker.addEventListener('mouseenter', (e) => {
                const name = e.target.dataset.name;
                const role = e.target.dataset.role;
                const location = e.target.dataset.location;
                const status = e.target.dataset.status;
                const time = e.target.dataset.time;

                tooltip.innerHTML = `
                    <strong>${name}</strong> (${role})<br>
                    位置: ${location}<br>
                    状态: ${status}<br>
                    更新: ${time}
                `;
                tooltip.style.display = 'block';
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            });

            marker.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });

            marker.addEventListener('click', (e) => {
                const name = e.target.dataset.name;
                MiningAI.showNotification(`已选择人员: ${name}`, 'info');
            });
        });

        // 地图控制按钮事件
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除其他按钮的active状态（仅对同组按钮）
                const group = e.target.closest('.control-group');
                if (group) {
                    group.querySelectorAll('.control-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                }

                const action = e.target.textContent.trim();
                MiningAI.showNotification(`${action} 功能已激活`, 'info');
            });
        });

        // 模拟实时健康数据更新
        MiningAI.simulateRealTimeData('avgHeartRate', 70, 85, 'bpm', 0);
        MiningAI.simulateRealTimeData('avgBodyTemp', 36.0, 37.0, '°C', 1);
        MiningAI.simulateRealTimeData('fatigueLevel', 10, 25, '%', 0);
        MiningAI.simulateRealTimeData('stressLevel', 20, 35, '%', 0);
        MiningAI.simulateRealTimeData('oxygenSat', 95, 99, '%', 0);
        MiningAI.simulateRealTimeData('activityLevel', 50, 80, '%', 0);

        // 添加闪烁动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0.3; }
            }
        `;
        document.head.appendChild(style);

        // 模拟人员位置更新
        setInterval(() => {
            personnelMarkers.forEach(marker => {
                // 随机微调位置，模拟人员移动
                const currentTop = parseFloat(marker.style.top);
                const currentLeft = parseFloat(marker.style.left);

                const newTop = currentTop + (Math.random() - 0.5) * 2;
                const newLeft = currentLeft + (Math.random() - 0.5) * 2;

                // 确保不超出边界
                if (newTop > 5 && newTop < 85) {
                    marker.style.top = newTop + '%';
                }
                if (newLeft > 5 && newLeft < 85) {
                    marker.style.left = newLeft + '%';
                }
            });
        }, 5000);
    </script>
</body>
</html>