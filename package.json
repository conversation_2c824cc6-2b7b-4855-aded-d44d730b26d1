{"name": "mining-ai-management-system", "version": "1.0.0", "description": "矿业AI智能监控平台 - 基于AI技术的矿业安全生产智能监控管理系统", "main": "index.html", "scripts": {"start": "python3 -m http.server 8000", "dev": "python3 -m http.server 8000", "serve": "npx http-server -p 8000 -c-1", "build": "echo 'No build process needed for static HTML project'", "test": "echo 'No tests specified'"}, "keywords": ["mining", "ai", "monitoring", "safety", "industrial", "iot", "edge-computing", "machine-learning", "dashboard", "visualization"], "author": "Mining AI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/mining-ai-management-system.git"}, "bugs": {"url": "https://github.com/your-username/mining-ai-management-system/issues"}, "homepage": "https://github.com/your-username/mining-ai-management-system#readme", "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}