<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡检管理 - 矿业AI管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .inspection-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .task-management {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .task-board {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .task-filters {
            display: flex;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-sm);
        }

        .filter-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
            cursor: pointer;
        }

        .task-item:hover {
            background: var(--bg-color);
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-info {
            flex: 1;
        }

        .task-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .task-details {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .task-status {
            text-align: right;
        }

        .task-progress {
            font-size: 11px;
            margin-top: 2px;
        }

        .progress-bar {
            width: 60px;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 2px;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-color);
            transition: width 0.3s ease;
        }

        .route-panel {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .route-map {
            height: 300px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
            border: 2px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .route-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .route-path {
            position: absolute;
            border: 3px dashed var(--primary-color);
            border-radius: 3px;
        }

        .checkpoint {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: var(--shadow-md);
        }

        .checkpoint-pending {
            background: var(--text-muted);
        }

        .checkpoint-completed {
            background: var(--success-color);
        }

        .checkpoint-current {
            background: var(--warning-color);
            animation: pulse 2s infinite;
        }

        .checkpoint-failed {
            background: var(--danger-color);
        }

        .route-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .mobile-interface {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .mobile-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .mobile-screen {
            width: 200px;
            height: 350px;
            background: #1a1a1a;
            border-radius: 20px;
            margin: 0 auto var(--spacing-sm);
            position: relative;
            overflow: hidden;
            border: 8px solid #333;
        }

        .mobile-content {
            padding: var(--spacing-sm);
            color: white;
            font-size: 12px;
        }

        .mobile-header {
            background: var(--primary-color);
            padding: var(--spacing-xs);
            text-align: center;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .mobile-button {
            background: var(--success-color);
            color: white;
            border: none;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            margin: 2px;
            font-size: 10px;
            cursor: pointer;
            width: calc(50% - 4px);
        }

        .qr-scanner {
            width: 80px;
            height: 80px;
            background: var(--bg-color);
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: var(--spacing-xs) auto;
        }

        .data-analysis {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-md);
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-sm);
        }

        .analysis-chart {
            height: 150px;
            background: var(--bg-color);
            border-radius: var(--radius-sm);
            margin: var(--spacing-sm) 0;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .trend-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            margin-top: 4px;
        }

        .trend-up {
            color: var(--danger-color);
        }

        .trend-down {
            color: var(--success-color);
        }

        .trend-stable {
            color: var(--text-secondary);
        }

        .issue-tracking {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .issue-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .issue-item:last-child {
            border-bottom: none;
        }

        .issue-info {
            flex: 1;
        }

        .issue-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 13px;
        }

        .issue-details {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .issue-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .status-open {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .status-progress {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-resolved {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .inspection-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
            display: none;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面布局
        const pageContent = MiningAI.initializeLayout('inspection-management.html', '巡检管理', ['首页', '巡检管理']);

        // 页面内容
        pageContent.innerHTML = `
            <!-- 巡检概览 -->
            <div class="inspection-overview">
                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">今日任务</div>
                                <div class="data-card-value" id="todayTasks">24 <span class="data-card-unit">项</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">完成率</div>
                                <div class="data-card-value" id="completionRate">87.5 <span class="data-card-unit">%</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">发现问题</div>
                                <div class="data-card-value" id="issuesFound">8 <span class="data-card-unit">个</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card data-card">
                    <div class="card-body">
                        <div class="data-card-header">
                            <div class="data-card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="data-card-info">
                                <div class="data-card-title">巡检人员</div>
                                <div class="data-card-value" id="inspectors">15 <span class="data-card-unit">人</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务管理 -->
            <div class="task-management">
                <div class="task-board">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-clipboard-list"></i>
                        巡检任务管理
                    </h3>
                    <div class="task-filters">
                        <button class="filter-btn active" data-filter="all">全部任务</button>
                        <button class="filter-btn" data-filter="pending">待执行</button>
                        <button class="filter-btn" data-filter="progress">进行中</button>
                        <button class="filter-btn" data-filter="completed">已完成</button>
                        <button class="filter-btn" data-filter="overdue">超期</button>
                    </div>
                    <div class="task-list">
                        <div class="task-item" data-status="progress">
                            <div class="task-info">
                                <div class="task-title">主通风机日常检查</div>
                                <div class="task-details">负责人: 张三 | 计划时间: 08:00-10:00</div>
                            </div>
                            <div class="task-status">
                                <span class="status-indicator status-warning">
                                    <i class="fas fa-clock"></i>
                                    进行中
                                </span>
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 60%;"></div>
                                    </div>
                                    60%
                                </div>
                            </div>
                        </div>
                        <div class="task-item" data-status="completed">
                            <div class="task-info">
                                <div class="task-title">皮带机安全检查</div>
                                <div class="task-details">负责人: 李四 | 计划时间: 06:00-08:00</div>
                            </div>
                            <div class="task-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-check-circle"></i>
                                    已完成
                                </span>
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 100%;"></div>
                                    </div>
                                    100%
                                </div>
                            </div>
                        </div>
                        <div class="task-item" data-status="pending">
                            <div class="task-info">
                                <div class="task-title">排水系统巡检</div>
                                <div class="task-details">负责人: 王五 | 计划时间: 10:00-12:00</div>
                            </div>
                            <div class="task-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-clock"></i>
                                    待执行
                                </span>
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%;"></div>
                                    </div>
                                    0%
                                </div>
                            </div>
                        </div>
                        <div class="task-item" data-status="overdue">
                            <div class="task-info">
                                <div class="task-title">瓦斯监测设备检查</div>
                                <div class="task-details">负责人: 赵六 | 计划时间: 昨天 14:00-16:00</div>
                            </div>
                            <div class="task-status">
                                <span class="status-indicator status-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    超期
                                </span>
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 30%;"></div>
                                    </div>
                                    30%
                                </div>
                            </div>
                        </div>
                        <div class="task-item" data-status="completed">
                            <div class="task-info">
                                <div class="task-title">电气设备安全检查</div>
                                <div class="task-details">负责人: 孙七 | 计划时间: 12:00-14:00</div>
                            </div>
                            <div class="task-status">
                                <span class="status-indicator status-normal">
                                    <i class="fas fa-check-circle"></i>
                                    已完成
                                </span>
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 100%;"></div>
                                    </div>
                                    100%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 巡检路线 -->
                <div class="route-panel">
                    <h3 class="card-title mb-2">
                        <i class="fas fa-route"></i>
                        巡检路线规划
                    </h3>
                    <div class="route-map">
                        <div class="route-grid"></div>

                        <!-- 巡检路径 -->
                        <div class="route-path" style="top: 15%; left: 10%; width: 25%; height: 0; border-top: 3px dashed var(--primary-color);"></div>
                        <div class="route-path" style="top: 15%; left: 35%; width: 0; height: 30%; border-left: 3px dashed var(--primary-color);"></div>
                        <div class="route-path" style="top: 45%; left: 35%; width: 30%; height: 0; border-top: 3px dashed var(--primary-color);"></div>
                        <div class="route-path" style="top: 45%; left: 65%; width: 0; height: 25%; border-left: 3px dashed var(--primary-color);"></div>
                        <div class="route-path" style="top: 70%; left: 65%; width: 25%; height: 0; border-top: 3px dashed var(--primary-color);"></div>

                        <!-- 检查点 -->
                        <div class="checkpoint checkpoint-completed" style="top: 10%; left: 8%;"
                             data-point="1" data-name="主入口检查点" data-status="已完成" data-time="08:15">1</div>
                        <div class="checkpoint checkpoint-completed" style="top: 10%; left: 33%;"
                             data-point="2" data-name="通风机房" data-status="已完成" data-time="08:45">2</div>
                        <div class="checkpoint checkpoint-current" style="top: 40%; left: 33%;"
                             data-point="3" data-name="主巷道中段" data-status="检查中" data-time="09:15">3</div>
                        <div class="checkpoint checkpoint-pending" style="top: 40%; left: 63%;"
                             data-point="4" data-name="皮带机房" data-status="待检查" data-time="">4</div>
                        <div class="checkpoint checkpoint-pending" style="top: 65%; left: 63%;"
                             data-point="5" data-name="排水泵房" data-status="待检查" data-time="">5</div>
                        <div class="checkpoint checkpoint-pending" style="top: 65%; left: 88%;"
                             data-point="6" data-name="末端检查点" data-status="待检查" data-time="">6</div>

                        <div class="inspection-tooltip" id="inspectionTooltip"></div>
                    </div>
                    <div class="route-info">
                        <span>总路线长度: 2.8km</span>
                        <span>预计用时: 4小时</span>
                        <span>当前进度: 3/6</span>
                    </div>
                </div>
            </div>

            <!-- 移动端巡检界面 -->
            <div class="mobile-interface">
                <div class="mobile-card">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-mobile-alt"></i>
                        手持终端界面
                    </h3>
                    <div class="mobile-screen">
                        <div class="mobile-content">
                            <div class="mobile-header">巡检助手</div>
                            <div style="margin-bottom: var(--spacing-xs);">
                                <strong>当前任务:</strong><br>
                                主通风机日常检查
                            </div>
                            <div style="margin-bottom: var(--spacing-xs);">
                                <strong>检查点:</strong> 3/6<br>
                                <strong>进度:</strong> 60%
                            </div>
                            <div class="qr-scanner">
                                <i class="fas fa-qrcode" style="font-size: 24px; color: var(--text-muted);"></i>
                            </div>
                            <div style="text-align: center; margin-bottom: var(--spacing-xs);">
                                扫描二维码签到
                            </div>
                            <button class="mobile-button">
                                <i class="fas fa-camera"></i> 拍照取证
                            </button>
                            <button class="mobile-button">
                                <i class="fas fa-microphone"></i> 语音记录
                            </button>
                            <button class="mobile-button">
                                <i class="fas fa-exclamation-triangle"></i> 报告问题
                            </button>
                            <button class="mobile-button">
                                <i class="fas fa-check"></i> 完成检查
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mobile-card">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-camera"></i>
                        拍照取证记录
                    </h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xs); margin-bottom: var(--spacing-sm);">
                        <div style="aspect-ratio: 1; background: var(--bg-color); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color);">
                            <i class="fas fa-image" style="font-size: 24px; color: var(--text-muted);"></i>
                        </div>
                        <div style="aspect-ratio: 1; background: var(--bg-color); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color);">
                            <i class="fas fa-image" style="font-size: 24px; color: var(--text-muted);"></i>
                        </div>
                        <div style="aspect-ratio: 1; background: var(--bg-color); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color);">
                            <i class="fas fa-image" style="font-size: 24px; color: var(--text-muted);"></i>
                        </div>
                        <div style="aspect-ratio: 1; background: var(--bg-color); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color);">
                            <i class="fas fa-plus" style="font-size: 24px; color: var(--text-muted);"></i>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">
                        <p><strong>AI分析结果:</strong></p>
                        <p>• 设备外观正常</p>
                        <p>• 未发现异常磨损</p>
                        <p>• 润滑状态良好</p>
                        <p>• 建议下次检查: 7天后</p>
                    </div>
                </div>

                <div class="mobile-card">
                    <h3 class="card-title mb-3">
                        <i class="fas fa-clipboard-check"></i>
                        检查清单
                    </h3>
                    <div style="font-size: 12px;">
                        <div style="display: flex; justify-content: space-between; padding: 4px 0; border-bottom: 1px solid var(--border-color);">
                            <span>设备外观检查</span>
                            <span style="color: var(--success-color);"><i class="fas fa-check"></i></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 4px 0; border-bottom: 1px solid var(--border-color);">
                            <span>运行参数记录</span>
                            <span style="color: var(--success-color);"><i class="fas fa-check"></i></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 4px 0; border-bottom: 1px solid var(--border-color);">
                            <span>安全防护检查</span>
                            <span style="color: var(--warning-color);"><i class="fas fa-clock"></i></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 4px 0; border-bottom: 1px solid var(--border-color);">
                            <span>环境条件评估</span>
                            <span style="color: var(--text-muted);"><i class="fas fa-minus"></i></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                            <span>维护建议记录</span>
                            <span style="color: var(--text-muted);"><i class="fas fa-minus"></i></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI数据分析 -->
            <div class="data-analysis">
                <h3 class="card-title mb-3">
                    <i class="fas fa-chart-line"></i>
                    AI巡检数据分析
                </h3>
                <div class="analysis-grid">
                    <div>
                        <h4 style="font-size: 14px; margin-bottom: var(--spacing-sm);">问题发现趋势</h4>
                        <div class="analysis-chart">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-area" style="font-size: 32px; color: var(--text-muted);"></i>
                            </div>
                        </div>
                        <div class="trend-indicator trend-down">
                            <i class="fas fa-arrow-down"></i>
                            <span>问题数量下降15%</span>
                        </div>
                    </div>

                    <div>
                        <h4 style="font-size: 14px; margin-bottom: var(--spacing-sm);">设备健康度评分</h4>
                        <div class="analysis-chart">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-pie" style="font-size: 32px; color: var(--text-muted);"></i>
                            </div>
                        </div>
                        <div class="trend-indicator trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>平均健康度提升8%</span>
                        </div>
                    </div>

                    <div>
                        <h4 style="font-size: 14px; margin-bottom: var(--spacing-sm);">巡检效率分析</h4>
                        <div class="analysis-chart">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-bar" style="font-size: 32px; color: var(--text-muted);"></i>
                            </div>
                        </div>
                        <div class="trend-indicator trend-stable">
                            <i class="fas fa-minus"></i>
                            <span>效率保持稳定</span>
                        </div>
                    </div>

                    <div>
                        <h4 style="font-size: 14px; margin-bottom: var(--spacing-sm);">预测性维护建议</h4>
                        <div style="font-size: 12px; color: var(--text-secondary); line-height: 1.5;">
                            <p><strong>AI预测结果:</strong></p>
                            <p>• 3号皮带机需要在7天内更换轴承</p>
                            <p>• 主通风机建议下周进行深度清洁</p>
                            <p>• 排水泵2号预计2周后需要维护</p>
                            <p>• 电气设备整体状态良好</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问题跟踪与闭环管理 -->
            <div class="issue-tracking">
                <h3 class="card-title mb-3">
                    <i class="fas fa-bug"></i>
                    问题跟踪与闭环管理
                </h3>
                <div class="issue-item">
                    <div class="issue-info">
                        <div class="issue-title">3号皮带机异常振动</div>
                        <div class="issue-details">发现时间: 2024-01-15 09:30 | 发现人: 李四 | 严重程度: 中等</div>
                    </div>
                    <div class="issue-status status-progress">处理中</div>
                </div>
                <div class="issue-item">
                    <div class="issue-info">
                        <div class="issue-title">主通风机润滑不足</div>
                        <div class="issue-details">发现时间: 2024-01-15 08:45 | 发现人: 张三 | 严重程度: 高</div>
                    </div>
                    <div class="issue-status status-open">待处理</div>
                </div>
                <div class="issue-item">
                    <div class="issue-info">
                        <div class="issue-title">排水泵房环境温度偏高</div>
                        <div class="issue-details">发现时间: 2024-01-14 16:20 | 发现人: 王五 | 严重程度: 低</div>
                    </div>
                    <div class="issue-status status-resolved">已解决</div>
                </div>
                <div class="issue-item">
                    <div class="issue-info">
                        <div class="issue-title">电气柜门锁损坏</div>
                        <div class="issue-details">发现时间: 2024-01-14 14:15 | 发现人: 孙七 | 严重程度: 中等</div>
                    </div>
                    <div class="issue-status status-resolved">已解决</div>
                </div>
                <div class="issue-item">
                    <div class="issue-info">
                        <div class="issue-title">安全标识牌缺失</div>
                        <div class="issue-details">发现时间: 2024-01-14 10:30 | 发现人: 赵六 | 严重程度: 中等</div>
                    </div>
                    <div class="issue-status status-progress">处理中</div>
                </div>
            </div>
        `;

        // 任务过滤功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除其他按钮的active状态
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                const filter = e.target.dataset.filter;
                const taskItems = document.querySelectorAll('.task-item');

                taskItems.forEach(item => {
                    if (filter === 'all' || item.dataset.status === filter) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });

                MiningAI.showNotification(`已筛选${e.target.textContent}`, 'info');
            });
        });

        // 检查点悬停提示
        const checkpoints = document.querySelectorAll('.checkpoint');
        const tooltip = document.getElementById('inspectionTooltip');

        checkpoints.forEach(checkpoint => {
            checkpoint.addEventListener('mouseenter', (e) => {
                const point = e.target.dataset.point;
                const name = e.target.dataset.name;
                const status = e.target.dataset.status;
                const time = e.target.dataset.time;

                tooltip.innerHTML = `
                    <strong>检查点 ${point}</strong><br>
                    ${name}<br>
                    状态: ${status}<br>
                    ${time ? `时间: ${time}` : ''}
                `;
                tooltip.style.display = 'block';
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            });

            checkpoint.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });

            checkpoint.addEventListener('click', (e) => {
                const point = e.target.dataset.point;
                const name = e.target.dataset.name;
                MiningAI.showNotification(`已选择检查点${point}: ${name}`, 'info');
            });
        });

        // 移动端按钮事件
        document.querySelectorAll('.mobile-button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.textContent.trim();
                MiningAI.showNotification(`${action}功能已激活`, 'info');
            });
        });

        // 任务项点击事件
        document.querySelectorAll('.task-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const title = e.target.closest('.task-item').querySelector('.task-title').textContent;
                MiningAI.showNotification(`已选择任务: ${title}`, 'info');
            });
        });

        // 模拟实时数据更新
        MiningAI.simulateRealTimeData('todayTasks', 20, 28, '项', 0);
        MiningAI.simulateRealTimeData('completionRate', 85, 95, '%', 1);
        MiningAI.simulateRealTimeData('issuesFound', 5, 12, '个', 0);
        MiningAI.simulateRealTimeData('inspectors', 12, 18, '人', 0);

        // 模拟任务进度更新
        setInterval(() => {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const currentWidth = parseFloat(bar.style.width);
                if (currentWidth < 100) {
                    const newWidth = Math.min(100, currentWidth + Math.random() * 5);
                    bar.style.width = newWidth + '%';

                    const progressText = bar.closest('.task-status').querySelector('.task-progress');
                    if (progressText) {
                        progressText.lastChild.textContent = Math.round(newWidth) + '%';
                    }
                }
            });
        }, 8000);

        // 显示系统启动消息
        setTimeout(() => {
            MiningAI.showNotification('巡检管理系统已加载', 'success');
        }, 1000);
    </script>
</body>
</html>
